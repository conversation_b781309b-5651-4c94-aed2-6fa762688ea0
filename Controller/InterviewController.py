from fastapi import APIRouter, UploadFile, File
from pydantic import BaseModel

from Agents.InterviewAgent import InterviewAgent
from Models.AjaxResult import AjaxResult

router = APIRouter(prefix="/agentService/api/interview", tags=["interview"])

class GenerateEvaluation(BaseModel):
    text: str

@router.post("/generateEvaluation")
async def generate_evaluation(request: GenerateEvaluation):
    res=InterviewAgent.generate_evaluation(request.text)
    return AjaxResult.success(res)


class GenerateEvaluationInInterview(BaseModel):
    history: str
    text: str
    id: str

@router.post("/generateEvaluationInInterview")
async def generate_evaluation_in_interview(request: GenerateEvaluationInInterview):
    llm_res=InterviewAgent.generate_evaluation_in_interview(request.text,request.history)
    res= {"id": request.id, "text": llm_res}
    return AjaxResult.success(res)
