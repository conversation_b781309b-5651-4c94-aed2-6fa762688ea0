import time
import uuid
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException

from Configs.Config import SysConfig
from Models.AjaxResult import AjaxResult
from Models.pojo.KBConversation import KBConversation
from Services.ConversationMemory import conversation_memory_manager
from Services.SqlServer.KBConversationService import KBConversationService
from Services.SummaryTitleService import SummaryTitleService
from Utils.KBAgentPool import kb_agent_pool

router = APIRouter(prefix="/agentService/api/conversation", tags=["conversation"])


# 获取服务实例
def get_service():
    return KBConversationService()


'''
获取会话列表
'''


@router.get("/list")
async def get_list(group_id, page_num: int = None,
                   page_size: int = None,
                   service: KBConversationService = Depends(get_service)):
    try:
        result,total = service.select_by_group_id(group_id, page_num, page_size)
        return AjaxResult.success_for_rows(total, result)
    except Exception as e:
        print(f"获取会话聊天记录: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
删除会话信息
'''


@router.post("/delete")
async def delete(json_data: dict, service: KBConversationService = Depends(get_service)):
    try:
        start_time = time.perf_counter()  # 记录开始时间

        # 确保request不为None
        if json_data is None:
            return AjaxResult.error("删除会话信息无效")
        if json_data["conversation_id"] is None:
            return AjaxResult.error("删除会话信息无效,conversation_id不能为空")
        # 删除会话信息
        result = service.delete(json_data["conversation_id"])
        if result != -1:
            # 删除会话全部聊天记录
            conversation_memory_manager.delete_conversation_details(user_flag=json_data["group_id"],data=json_data["conversation_id"])

            end_time = time.perf_counter()  # 记录结束时间
            print(f"运行时间: {end_time - start_time}秒")

            return AjaxResult.success(result)

        return AjaxResult.error("Not Found")
    except Exception as e:
        print(f"会话列表信息异常: {str(e)}")
        raise AjaxResult.handle_exception(e)


@router.get("/getinfo/{id}")
async def get_info(id: str, service: KBConversationService = Depends(get_service)):
    try:
        # 确保request不为None
        if id is None:
            return AjaxResult.error("请求会话信息无效")
        result = service.select_by_id(id)
        if result:
            return AjaxResult.success(result)
        return AjaxResult.error("Not Found")
    except Exception as e:
        print(f"获取会话详情异常: {str(e)}")
        raise AjaxResult.handle_exception(e)


@router.post("/editTitle")
async def edit(json_data: dict,
               service: KBConversationService = Depends(get_service)):
    try:
        if json_data is None:
            return AjaxResult.error("更新无效")
        if json_data["conversation_id"] is None:
            return AjaxResult.error("更新会话信息无效,conversation_id不能为空")
        if json_data["title"] is None:
            return AjaxResult.error("更新会话信息无效,title不能为空")

        params = KBConversation(id=json_data["conversation_id"], title=json_data["title"])

        result = service.update(params)
        if result:
            return AjaxResult.success(result)
        return AjaxResult.error("Not Found")
    except Exception as e:
        print(f"更新会话信息异常: {str(e)}")
        raise AjaxResult.handle_exception(e)


# 新建会话
@router.post("/new")
async def new(json_data: dict, service: KBConversationService = Depends(get_service)):
    try:
        # 确保request不为None
        if json_data is None:
            return AjaxResult.error("创建会话失败,当前参数异常")
        if json_data.get("group_id") is None:
            return AjaxResult.error("创建会话失败,group_id不能为空")
        # 会话id
        conversation_id = str(uuid.uuid4())
        # 会话对象
        conversation_data = KBConversation(
            id=conversation_id,
            title="新建聊天对话",
            group_id=json_data["group_id"],
        )
        result = service.insert(conversation_data)
        if result:
            return AjaxResult.success(conversation_data)
        return AjaxResult.error("Not Found")
    except Exception as e:
        print(f"更新会话信息异常: {str(e)}")
        raise AjaxResult.handle_exception(e)


@router.get("/title")
async def get_title(id: str, service: KBConversationService = Depends(get_service)):
    try:
        # 此处目前只处理了在缓存内的agent，对于已经移除缓存的agent会找不到
        agent = kb_agent_pool.get_agent(id)
        if agent is not None:
            title_service = SummaryTitleService()
            message_list= agent.get_messages()
            title=None
            if message_list:
                title = title_service.generate_title(message_list)

            # 如果标题为空，返回 204
            if title is None:
                return AjaxResult.customize_error(
                    code=508,
                    message="对话内容无效或不完整"
                )

            # 保存当前消息的标题
            data = KBConversation(id=id, title=title)
            service.update(data)
            return AjaxResult.customize_success(
                code=200,
                message="success",
                data={
                    "title": title,
                    "id": id
                }
            )
        return AjaxResult.customize_error(
            code=508,
            message="没有找到相应的对话"
        )
    except Exception as e:
        print(f"获取会话历史记录时出错: {str(e)}")
        raise HTTPException(status_code=508, detail=str(e))

@router.get("/version")
async def version():
    return AjaxResult.success(SysConfig["version"])