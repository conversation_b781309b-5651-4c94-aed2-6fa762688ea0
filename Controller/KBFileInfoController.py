import re
import uuid
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, Query, Path
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from Models.AjaxResult import AjaxResult
from Models.pojo import KBFileTrunkInfo
from Models.pojo.KBFileInfo import KBFileInfo
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorSaveService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService

router = APIRouter(prefix="/agentService/api/kbFile", tags=["kbFile"])


# 获取服务实例
def get_service():
    return KBFileInfoService()


# 获取服务实例
def get_service2():
    return KBFileTrunkInfoService()


'''
查询文件列表
'''


@router.get("/list")
async def get_file_data(
        # 使用独立参数接收查询条件
        kb_id: int = Query(..., title="知识库ID"),
        file_name: Optional[str] = Query(None, title="文件名称"),
        file_state: Optional[int] = Query(None, title="文件状态"),
        file_enable: Optional[int] = Query(None, title="启用状态"),
        page_num: int = Query(1, alias="pageNumber", ge=1, title="页码"),
        page_size: int = Query(10, alias="pageSize", ge=1, title="每页数量"),
        service: KBFileInfoService = Depends(get_service)
):
    try:
        # 构建查询参数对象
        params = KBFileInfo()

        params.kb_id = kb_id
        if file_name is not None:
            params.file_name = f"%{file_name}%"
        if file_state is not None:
            params.file_state = file_state
        if file_enable is not None:
            params.file_enable = file_enable

        list, total = service.select_all(params, page_num, page_size)
        return AjaxResult.success({
            "list": jsonable_encoder(list),
            "total": total,
            "pageNum": page_num,
            "pageSize": page_size
        })
    except Exception as e:
        print(f"获取文件列表失败: {str(e)}")
        return AjaxResult.error(str(e))


'''
新增文件
'''


class KBFileBatchCreateRequest(BaseModel):
    kb_id: int
    files: list[KBFileInfo]


@router.post("/add")
async def insert_file_data(
        request: KBFileBatchCreateRequest,
        service: KBFileInfoService = Depends(get_service)
):
    try:
        results = []

        for file_info in request.files:
            # 添加默认值
            file_info.kb_id = request.kb_id
            # 数据库插入
            result = service.insert(file_info)

            if result < 1:
                return AjaxResult.error("部分文件插入失败")

            results.append(file_info.dict())

        return AjaxResult.success({
            "kb_id": request.kb_id,
            "inserted_files": results
        })

    except Exception as e:
        print(f"批量插入出错: {str(e)}")
        return AjaxResult.error(str(e))


'''
修改文件信息
'''


class KBFileUpdateFileRequest(BaseModel):
    file_id: str
    file_name: Optional[str] = None
    allow_download: Optional[int] = None
    file_enable: Optional[int] = None


@router.post("/updateFile")
async def update_file(
        request: KBFileUpdateFileRequest,
        service: KBFileInfoService = Depends(get_service)
):
    try:
        # 获取当前文件信息
        file_info = service.select_by_id(request.file_id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        # 仅更新传入的字段
        if request.file_name is not None:
            file_info.file_name = request.file_name
        if request.allow_download is not None:
            file_info.allow_download = request.allow_download
        if request.file_enable is not None:
            file_info.file_enable = request.file_enable

        # 执行更新
        result = service.update(file_info)
        return AjaxResult.success(data={
            "id": request.file_id,
            "updated_fields": file_info.dict(exclude_unset=True)
        })
    except Exception as e:
        print(f"修改文件信息时出错: {str(e)}")
        return AjaxResult.error(str(e))


'''
获取文件详情
'''


@router.get("/getFileInfo")
async def get_file_info(
        # 使用Query参数接收数组（多个同名参数）
        file_ids: str = None,
        service: KBFileInfoService = Depends(get_service)
):
    try:
        if not file_ids:
            return AjaxResult.error("至少需要提供一个文件ID")

        # 将文件ID列表转换为列表
        file_ids_list = file_ids.split(',')

        # 获取文件详情
        results = []
        for id in file_ids_list:
            result = service.select_by_id(id)
            if result:
                results.append(result)

        return AjaxResult.success(results)
    except Exception as e:
        print(f"获取文件详情时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
删除文件
'''


@router.delete("/delete")
async def remove_file_data(
        # 使用Query参数接收数组（多个同名参数）
        file_ids: str = None,
        kb_ids: str = None,
        service: KBFileInfoService = Depends(get_service)
):
    try:
        if not file_ids and not kb_ids:
            return AjaxResult.error("至少需要提供一个删除条件")

        result = service.batch_delete(
            file_ids=file_ids,
            kb_ids=kb_ids
        )

        return AjaxResult.success({"deleted_count": result})
    except Exception as e:
        return AjaxResult.error(str(e))


'''
重试解析失败的知识库文件
'''


@router.post("/retryParse/{id}")
async def retry_parse(id: str, service: KBFileInfoService = Depends(get_service)):
    try:
        # 获取当前文件信息
        file_info = service.select_by_id(id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        file_info.file_state = 0
        file_info.update_time = datetime.now()

        result = service.update(file_info)
        return AjaxResult.success(data={"id": id, "row": result})
    except Exception as e:
        print(f"重试解析失败的知识库文件时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
获取文件分块列表
'''


@router.get("/getFileTrunkList")
async def get_file_trunk_list(
        file_id: str = Query(..., title="文件ID"),
        trunk_state: Optional[int] = Query(None, title="分块状态"),
        page_num: int = Query(1, alias="pageNumber", ge=1, title="页码"),
        page_size: int = Query(10, alias="pageSize", ge=1, title="每页数量"),
        service: KBFileTrunkInfoService = Depends(get_service2)
):
    try:
        params = KBFileTrunkInfo()
        params.file_id = file_id
        if trunk_state is not None:
            params.state = trunk_state

        list, total = service.select_all(params, page_num, page_size)
        return AjaxResult.success({
            "list": jsonable_encoder(list),
            "total": total,
            "pageNum": page_num,
            "pageSize": page_size
        })
    except Exception as e:
        print(f"获取文件分块列表失败: {str(e)}")
        return AjaxResult.error(str(e))


'''
获取文件分块添加
'''


class KBFileTrunkAddRequest(BaseModel):
    file_id: str = None
    kb_id: int = None
    content: str = None
    keywords: str = None
    meta_data: str = None


@router.post("/addTrunk")
async def add_trunk(request: KBFileTrunkAddRequest, service: KBFileTrunkInfoService = Depends(get_service2)):
    try:
        # 添加默认值
        trunk_info = KBFileTrunkInfo()
        trunk_info.id = str(uuid.uuid4())
        trunk_info.state = 0

        # 文件ID不能为空
        if request.file_id is not None:
            trunk_info.file_id = request.file_id
        else:
            return AjaxResult.error("文件ID不能为空")

        # 知识库ID不能为空
        if request.kb_id is not None:
            trunk_info.kb_id = request.kb_id
        else:
            return AjaxResult.error("知识库ID不能为空")

        if request.content is not None:
            trunk_info.content = request.content
        if request.keywords is not None:
            trunk_info.keywords = request.keywords
        if request.meta_data is not None:
            trunk_info.meta_data = request.meta_data

        result = service.insert(trunk_info)
        return AjaxResult.success(data={"id": trunk_info.id, "add_trunk": trunk_info})
    except Exception as e:
        print(f"文件分块添加时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
文件分块编辑
'''


@router.post("/updateTrunk")
async def update_trunk(request: KBFileTrunkInfo, service: KBFileTrunkInfoService = Depends(get_service2)):
    try:
        result = service.update(request)
        return AjaxResult.success(data={"id": request.id, "row": result})
    except Exception as e:
        print(f"文件分块编辑时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
删除文件分块
'''


@router.delete("/deleteTrunk")
async def remove_file_data(
        # 使用Query参数接收数组（多个同名参数）
        ids: str = None,
        file_ids: str = None,
        kb_ids: str = None,
        service: KBFileTrunkInfoService = Depends(get_service2)
):
    try:
        if not ids and not file_ids and not kb_ids:
            return AjaxResult.error("至少需要提供一个删除条件")

        result = service.batch_delete(
            ids=ids
        )
        return AjaxResult.success({"deleted_count": result})
    except Exception as e:
        return AjaxResult.error(str(e))


######################### 前端接口 ###############################

'''
查询文件列表
'''


@router.get("/file/list")
async def get_file_list(
        # 使用独立参数接收查询条件
        kbId: int = Query(..., title="知识库ID"),
        fileName: Optional[str] = Query(None, title="文件名称"),
        status: Optional[str] = Query(None, title="文件状态"),
        pageNum: int = Query(1, alias="pageNum", ge=1, title="页码"),
        pageSize: int = Query(10, alias="pageSize", ge=1, title="每页数量"),
        service: KBFileInfoService = Depends(get_service)
):
    try:
        # 构建查询参数对象
        params = KBFileInfo()
        params.kb_id = kbId
        if fileName is not None and fileName != "":
            params.file_name = f"%{fileName}%"
        if status is not None and status != "":
            params.file_state = int(status)

        list, total = service.select_all(params, pageNum, pageSize)
        return AjaxResult.success_for_rows(total, jsonable_encoder([file.to_dict_vo() for file in list]))
    except Exception as e:
        print(f"获取文件列表失败: {str(e)}")
        return AjaxResult.error(str(e))


'''
查询文件详情
'''


@router.get("/file/{id}")
async def get_file_list(
        # 使用独立参数接收查询条件
        id: str = Path(..., title="文件ID"),
        service: KBFileInfoService = Depends(get_service)
):
    try:
        file_info = service.select_by_id(id)
        if not file_info:
            return AjaxResult.error("文件不存在")
        return AjaxResult.success_for_rows(1, jsonable_encoder(file_info.to_dict()))
    except Exception as e:
        print(f"获取文件详情失败: {str(e)}")
        return AjaxResult.error(str(e))


class KBFileUpdateFileRequest(BaseModel):
    id: str  # 文件ID
    fileName: Optional[str] = None  # 文件名称
    isAvailable: Optional[int] = None  # 启用状态
    isDownload: Optional[int] = None  # 下载权限


'''
知识库文件编辑-修改名称
'''


@router.put("/file")
async def file_update(request: KBFileUpdateFileRequest, service: KBFileInfoService = Depends(get_service)):
    try:
        file_info = service.select_by_id(request.id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        if request.fileName is not None:
            file_info.file_name = request.fileName

        result = service.update(file_info)
        if result < 1:
            return AjaxResult.error("修改失败")
        else:
            return AjaxResult.success()
    except Exception as e:
        print(f"修改文件名称时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
知识库文件编辑-下载权限
'''


@router.put("/file/isDownload")
async def file_update(request: KBFileUpdateFileRequest, service: KBFileInfoService = Depends(get_service)):
    try:
        file_info = service.select_by_id(request.id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        if request.isDownload is not None:
            file_info.allow_download = request.isDownload

        result = service.update(file_info)
        if result < 1:
            return AjaxResult.error("修改失败")
        else:
            return AjaxResult.success()
    except Exception as e:
        print(f"修改文件下载权限时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
知识库文件-下载
'''


@router.get("/download")
def file_download(file_ids: str = Query(..., title="文件ids"), service: KBFileInfoService = Depends(get_service)):
    try:
        file_ids = file_ids.split(",")
        files = service.select_by_file_ids(file_ids)
        if files is not None and len(files) == 0:
            return AjaxResult.error("文件不存在")
        # 提取文件内容列表
        return AjaxResult.success(jsonable_encoder([file.to_download() for file in files]))
    except Exception as e:
        print(f"修改文件下载权限时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
知识库文件编辑-修改启用状态
'''


@router.put("/file/isEnable")
async def file_update(request: KBFileUpdateFileRequest, service: KBFileInfoService = Depends(get_service)):
    try:
        file_info = service.select_by_id(request.id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        if request.isAvailable is not None:
            file_info.file_enable = request.isAvailable

        result = service.update(file_info)
        if result < 1:
            return AjaxResult.error("修改失败")
        else:
            return AjaxResult.success()
    except Exception as e:
        print(f"修改文件启用状态时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
重试解析失败的知识库文件
'''


@router.post("/file/retryParse/{id}")
async def retry_parse(id: str, service: KBFileInfoService = Depends(get_service)):
    try:
        # 获取当前文件信息
        file_info = service.select_by_id(id)
        if not file_info:
            return AjaxResult.error("文件不存在")

        file_info.file_state = 0

        result = service.update(file_info)
        if result < 1:
            return AjaxResult.error("重试解析失败")
        else:
            return AjaxResult.success()
    except Exception as e:
        print(f"重试解析失败的知识库文件时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)


'''
获取文件分块列表
'''


@router.get("/file/trunks/{id}")
async def get_file_trunk_list(
        id: str = Path(..., title="文件ID"),
        trunkState: Optional[int] = Query(None, title="分块状态"),
        content: Optional[str] = Query(None, title="分块内容"),
        pageNum: int = Query(1, alias="pageNum", ge=1, title="页码"),
        pageSize: int = Query(10, alias="pageSize", ge=1, title="每页数量"),
        service: KBFileTrunkInfoService = Depends(get_service2)
):
    try:
        params = KBFileTrunkInfo()
        params.file_id = id
        if trunkState is not None and trunkState != "":
            params.status = trunkState
        if content and content.strip():
            # 转义特殊字符
            safe_content = content.strip().replace("%", "\\%").replace("_", "\\_")
            # 处理中间连续空格
            safe_content = re.sub(r'\s+', '%', safe_content)
            params.content = f"%{safe_content}%"

        list, total = service.select_all(params, pageNum, pageSize)
        return AjaxResult.success_for_rows(total, jsonable_encoder([trunk.to_dict() for trunk in list]))
    except Exception as e:
        print(f"获取文件分块列表失败: {str(e)}")
        return AjaxResult.error(str(e))


# 校验是否存有正在解析的文件
@router.get("/isParsing/{kb_id}")
def is_parsing(kb_id=Path(..., title="标题"), service: KBFileInfoService = Depends(get_service)):
    try:
        list = service.select_by_kb_id(kb_id, 2)
        if list is not None and len(list) > 0:
            return AjaxResult.error("当前知识库中存有未完成解析文件")
        return AjaxResult.success()
    except Exception as e:
        print(f"校验是否存有正在解析的文件时出错: {str(e)}")
        raise AjaxResult.handle_exception(e)
