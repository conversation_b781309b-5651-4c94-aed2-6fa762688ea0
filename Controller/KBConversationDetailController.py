from fastapi import APIRouter

from Models.AjaxResult import AjaxResult
from Services.ConversationMemory import conversation_memory_manager

router = APIRouter(prefix="/agentService/api/conversationDetail", tags=["conversationDetail"])

'''
获取会话聊天记录
'''


@router.get("/list")
async def get_list(conversation_id: str = None, group_id:str = None, page_num: int = None,
                   page_size: int = None):
    try:
        # 确保request不为None
        if conversation_id is None:
            return AjaxResult.error("请求会话无效")
        result,total = conversation_memory_manager.query_conversation_details(user_flag=group_id,conversation_id=conversation_id, page_num=page_num, page_size=page_size)
        return AjaxResult.success_for_rows(total, result)
    except Exception as e:
        print(f"获取会话聊天记录: {str(e)}")
        raise AjaxResult.handle_exception(e)
