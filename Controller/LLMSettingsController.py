from fastapi import APIRouter
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from Configs.Config import SysConfig
from Models.AjaxResult import AjaxResult

router = APIRouter(prefix="/agentService/api/llmsetting", tags=["llmsetting"])


class LLMModelSetting(BaseModel):
    name: str
    model_think: str


@router.get("/list")
async def llm_list():
    model_list = []
    for section_key, section_value in SysConfig["llm"].items():
        if isinstance(section_value, dict):
            can_use: bool = bool(section_value.get("model_use_web", False))
            if can_use:
                model_list.append(LLMModelSetting(name=section_key, model_think=section_value.get("model_think")))

    return AjaxResult.success(jsonable_encoder(model_list))
