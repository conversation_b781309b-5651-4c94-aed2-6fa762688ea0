{"flag": "dev", "explain": "测试环境", "version": "1.0.0", "log_dir": "D:/qyd_agents/logs", "cross_domain": false, "search_result_filtering": false, "llm": {"chat-use": "deepseek-r1", "summary-title-use": "qwen", "talent-use": "qwen3", "qwen": {"engine": "ollama", "base_url": "http://172.16.20.103:11434", "model": "qwen2.5:14b", "model_think": "no_think", "model_use_web": true, "max_tokens": 32000}, "qwen3": {"engine": "ollama", "base_url": "http://*************:11434", "model": "qwen3:4b", "model_think": "no_think", "model_use_web": true, "max_tokens": 40000}, "deepseek-r1": {"engine": "ollama", "base_url": "http://172.16.20.103:11434", "model": "qwen3:8b", "model_think": "think", "model_use_web": true, "max_tokens": 128000}}, "embedding": {"name": "ollama", "ollama": {"path": "http://172.16.20.103:11434", "model": "bge-m3", "max_tokens": 8000}, "lmstudio": {"path": "http://*************:1444/v1", "model": "text-embedding-bge-m3"}}, "vector_db_type": "mil<PERSON><PERSON>", "retrievers": {"ensemble": {"k": 10, "score_threshold": 0.7, "fetch_k": 10, "bm25_weight": 0.3}, "vector": {"k": 5, "score_threshold": 0.7, "fetch_k": 10}}, "agents": {"memory_path": "D:/qyd_agents/memos", "kb_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 1500}, "expert_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 1500}, "question_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 3000, "mention_percent": 0.25, "min_mention_chars": 2500}}, "faiss": {"save_path": "D:/qyd_agents/faiss"}, "nacos": {"enabled": true, "server_addresses": "http://localhost:8848", "namespace": "10f65034-51d6-4eb8-8bb8-b057cf130584", "group": "DEFAULT_GROUP", "username": "nacos", "password": "nacos", "service": {"service_name": "agent-service", "ip": "************", "port": 8000, "cluster_name": "DEFAULT", "group_name": "DEFAULT_GROUP", "metadata": {"preserved.register.source": "PYTHON"}}}, "mysql": {"database": "kb_dev", "user": "root", "password": "root", "host": "localhost", "port": 3306, "connectionLimit": 10}, "vector_gen": {"enabled": true, "cache_dir": "D:/qyd_agents/vector_gen_cache", "temp_dir": "D:/qyd_agents/vector_gen_temp", "concurrency": 1}, "sqlite": {"enabled": true, "database": "D:/qyd_agents/sqliteDB", "db_name": "xiao_sqlite"}, "milvus": {"host": "************", "port": "19530", "user": "root", "password": "mil<PERSON><PERSON>", "db_name": "default", "collection_name": "xt1_collection"}, "minio": {"endpoint": "*************:9000", "access_key": "minioadmin", "secret_key": "minioadmin", "bucket": "bucket01", "secure": false}, "pipeline": {"enabled": true, "cache_dir": "D:/qyd_agents/pipeline"}, "talent": {"talent_url": "http://************:8080", "file_dir": "D:/qyd_agents/talent"}}