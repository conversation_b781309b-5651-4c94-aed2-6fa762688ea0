import aiofiles
from aiofiles import os

from Configs.Config import SysConfig
from Services.ContentLoader.LoaderFactory import LoaderFactory
from Services.TaskServer.BaseGenTask import BaseGenTask
from Utils.FileDownloader import FileDownloader


class TalentParseWorker(BaseGenTask):
    def __init__(self, fileUrl: str):
        super().__init__(fileUrl)
        # 服务实例
        self.temp_dir = SysConfig["talent"]["file_dir"]
        self.file_downloader = FileDownloader(self.temp_dir)

    async def _task(self):

        # TODO: 后续增加队列和任务管理表，目前先简单实现转换

        # 下载文件
        local_file_url = self.file_downloader.download(self._file_url)

        # 基础校验
        if not await aiofiles.os.path.exists(local_file_url):
            raise FileNotFoundError(f"文件不存在: {local_file_url}")

        # 初始化文件加载器
        loader = LoaderFactory.get_file_loader(local_file_url, 400)
        if not loader:
            raise ValueError(f"不支持的文件类型: {local_file_url}")
        # 开始任务
        docs = loader.load(file_name="")
        if not docs:
            raise ValueError(f"文件加载失败: {local_file_url}")

        # 创建内容块
        content = ""
        for doc in docs:
            content += doc.page_content
        self.file_downloader.remove(self._file_url)
        return content

    def _update_task_state(self, *args, **kwargs):
        """
        实现抽象方法 _update_task_state。
        这里可以根据实际业务需求添加具体逻辑，
        目前暂时不做任何操作。
        """
        pass

