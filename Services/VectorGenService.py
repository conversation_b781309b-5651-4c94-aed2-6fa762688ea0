import asyncio
import os
from typing import List, Union

from langchain_core.documents import Document

from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.ContentLoader.LoaderFactory import LoaderFactory
from Services.KBTrunkVectorService.CustomBM25Retriever import chinese_keywords
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorSaveService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Utils.CommonUtils import list_to_str
from Utils.logs.LoggingConfig import logger


class VectorGenService:
    """向量生成服务"""

    def __init__(self):
        self.__kb_file_trunk_service = KBFileTrunkInfoService()
        self.__kb_file_service = KBFileInfoService()
        self.__kb_trunk_list = []

    # 转化对象
    def __convert_to_kb_file_trunk(self, trunks: List[Document], file_id: str,
                                   knowledge_id: int) -> List[KBFileTrunkInfo]:
        trunk_list: List[KBFileTrunkInfo] = []

        for trunk in trunks:
            if trunk.page_content:
                file_data = KBFileTrunkInfo(id=trunk.id,
                                            file_id=file_id,
                                            kb_id=knowledge_id,
                                            content=trunk.page_content,
                                            keywords=list_to_str(chinese_keywords(trunk.page_content)),
                                            meta_data=trunk.metadata, state=0)
                trunk_list.append(file_data)
        return trunk_list

    async def gen_vectors(self, kb_id: int, file_id: str, file_path: str) -> Union[int, str]:
        """生成文件向量并持久化存储
        Args:
            kb_id: 知识库ID
            file_id: 文件唯一标识
            file_path: 本地文件路径
        Returns:
            int: 成功存储的分块数量
            str: 错误信息（失败时返回）
        """
        try:
            logger.info(f"开始分块 [{file_id}]")

            # 基础校验
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 初始化文件加载器
            loader = LoaderFactory.get_file_loader(file_path,400)
            if not loader:
                raise ValueError(f"不支持的文件类型: {file_path}")

            try:
                # 清理历史分块数据
                if self.__kb_file_trunk_service.count_by_file_id(file_id) > 0:
                    logger.info(f"清理历史分块数据 [{file_id}]")
                    self.__kb_file_trunk_service.batch_delete(file_ids=file_id)
                    TrunkVectorSaveService.delete_by_file_id(file_id)

                # 获取文件元信息
                if not (file_info := self.__kb_file_service.select_by_id(file_id)):
                    raise ValueError("文件记录不存在")

                #性能瓶颈在向量化上，异步按照任务处理反而拖慢了进度。
                documents=loader.load(file_info.file_name)
                self.__save_trunks_async(documents, file_id, kb_id)

                # 持久化到数据库
                if (trunk_count := self.__save_trunks_db()) > 0:
                    logger.info(f"成功存储 {trunk_count} 个分块 [{file_id}]")
                    return trunk_count

                raise RuntimeError("未生成有效分块数据")

            except Exception as e:
                logger.error(f"文件处理失败 [{file_id}]: {str(e)}")
                raise e
            finally:
                del loader  # 确保释放文件加载器资源

        except Exception as e:
            logger.error(f"向量生成流程异常 [{file_id}]: {str(e)}")
            raise e

    def __save_trunks_async(self, trunks: List[Document], file_id: str, kb_id: int):
        """异步保存文档分块和向量"""
        logger.info(f"开始提取关键词")
        file_trunk_list = self.__convert_to_kb_file_trunk(trunks, file_id, kb_id)
        logger.info(f"结束提取关键词")
        self.__kb_trunk_list.extend(file_trunk_list)
        logger.info(f"开始向量存储")
        TrunkVectorSaveService.save_trunks(file_trunk_list)
        logger.info(f"结束向量存储")

    def __save_trunks_db(self) -> int:
        trunk_count = 0
        if len(self.__kb_trunk_list) > 0:
            # 保存文件分块，每次插入50条
            batch_size = 50
            for i in range(0, len(self.__kb_trunk_list), batch_size):
                batch = self.__kb_trunk_list[i:i + batch_size]
                # 增加序号
                for index, item in enumerate(batch):
                    item.sort = str(trunk_count + index + 1)
                self.__kb_file_trunk_service.insert_batch(batch)
                trunk_count += len(batch)
        return trunk_count
