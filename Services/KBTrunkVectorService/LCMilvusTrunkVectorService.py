import json
from typing import List

from langchain.retrievers import <PERSON><PERSON><PERSON>riever, ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainFilter
from langchain_core.documents import Document
from langchain_core.runnables import <PERSON>nableLambda
from langchain_milvus import Milvus
from pymilvus import (MilvusException)
from Configs.Config import SysConfig
from LLM.EmbeddingsManager import EmbeddingsManager
from LLM.LLMManager import sys_llm_manager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.KBTrunkVectorService.CustomBM25Retriever import get_bm25_retriever
from Services.KBTrunkVectorService.KBTrunkVectorBaseService import KBTrunkVectorUpdateService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger


class LCMilvusTrunkVectorService(KBTrunkVectorUpdateService):

    def __init__(self, embeddings_helper: EmbeddingsManager):
        super().__init__(embeddings_helper)

        # 检索配置
        self.__search_result_filtering = SysConfig["search_result_filtering"]
        # 从配置文件中获取 Milvus 连接信息
        self.__config_dto = SysConfig["milvus"]
        self.__collection_name = self.__config_dto["collection_name"]
        self.__db_name = self.__config_dto["db_name"]

        self.__total_k: int = SysConfig["retrievers"]["ensemble"]["k"]
        self.__k: int = SysConfig["retrievers"]["vector"]["k"]
        self.__score_threshold: float = SysConfig["retrievers"]["vector"]["score_threshold"]
        self.__fetch_k: int = SysConfig["retrievers"]["vector"]["fetch_k"]
        self.__bm25_weight: float = SysConfig["retrievers"]["ensemble"]["bm25_weight"]

    def _init_milvus_connection(self, config_dto, collection_name: str = None) -> bool:
        """初始化 Milvus 连接"""
        try:
            # 连接到 Milvus 集群
            self.milvus = Milvus(
                embedding_function=self._embeddings,
                connection_args={
                    "uri": f"http://{config_dto['host']}:{config_dto['port']}",  # 添加协议头
                    "host": config_dto["host"],
                    "port": config_dto["port"],
                    "user": config_dto["user"],
                    "password": config_dto["password"],
                },
                collection_name=collection_name if collection_name else config_dto["collection_name"],
                primary_field="id",  # 明确指定主键字段
                auto_id=False,  # 禁用自动生成ID
                consistency_level="Strong",
                index_params={
                    "metric_type": "L2",
                    "index_type": "IVF_FLAT",
                    "params": {"nlist": 128, "nprobe": 16}
                }
            )
            logger.info(f"Successfully connected to Milvus at {config_dto}")
            return True
        except MilvusException as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            return False

    def _get_collection(self, collection_name: str = None) -> Milvus | None:
        # 创建集合并存储向量
        if self.__collection_name == collection_name:
            return self.milvus
        else:
            self._init_milvus_connection(config_dto=self.__config_dto, collection_name=collection_name)
            self.__collection_name = collection_name
            return self.milvus

    def vector_base_init(self) -> bool:
        return self._init_milvus_connection(self.__config_dto)

    def update_trunk(self, trunk: KBFileTrunkInfo) -> bool:
        self.milvus.upsert(ids=[trunk.id], documents=[KBFileTrunkInfoService.transform_to_document(trunk)])
        return True

    def delete_trunk(self, trunk_id: str) -> bool:
        if self.milvus.col is None:
            return False
        self.milvus.delete(ids=[trunk_id])
        return True

    def save_trunks(self, trunks: List[KBFileTrunkInfo]) -> List[KBFileTrunkInfo]:
        documents = []
        ids = []
        for trunk in trunks:
            documents.append(KBFileTrunkInfoService.transform_to_document(trunk))
            ids.append(trunk.id)
            trunk.stored_vector_id = trunk.id

        self.milvus.add_documents(ids=ids, documents=documents)
        return trunks

    def hybrid_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        logger.info(f"开始进行检索，query:{query}\nfile_ids:{file_ids}")
        bm25_retriever = get_bm25_retriever(file_ids=file_ids)
        if self.milvus.col is None:
            return bm25_retriever.invoke(query)
        vector_retriever = self.milvus.as_retriever(search_type="similarity_score_threshold",
                                                    search_kwargs={
                                                        "k": self.__k,
                                                        "score_threshold": self.__score_threshold,
                                                        "expr": f"file_id in {json.dumps(file_ids)}" if file_ids else ""
                                                    })
        ensemble_retriever = EnsembleRetriever(
            retrievers=[bm25_retriever, vector_retriever],
            weights=[self.__bm25_weight, (1 - self.__bm25_weight)])
        # 大模型处理文本，效果很好，资源消耗也比较大
        if self.__search_result_filtering:
            llm = sys_llm_manager.get_summary_title_use_llm_helper().get_llm_object(0.3)
            llm = llm | RunnableLambda(remove_think_tags)
            compressor = LLMChainFilter.from_llm(llm)
            compression_retriever = ContextualCompressionRetriever(
                base_compressor=compressor, base_retriever=ensemble_retriever
            )
            search_res = compression_retriever.invoke(query)
        else:
            search_res = ensemble_retriever.invoke(query)
        logger.info(f"检索结果，query:{query}\ndocuments:{search_res}")
        return search_res[:self.__total_k]

    def delete_by_file_id(self, file_id: str) -> bool:
        if self.milvus.col is None:
            return False
        self.milvus.delete(expr=f'file_id == "{file_id}"')
        return True
