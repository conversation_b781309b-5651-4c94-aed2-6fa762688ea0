import json  # 添加json模块导入
from typing import List, Tuple

from langchain_core.documents import Document

from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Utils.DBTools import MySQLInitializer, MySQLBuilderEnhanced
from Utils.logs.LoggingConfig import logger

from Services.SqlServer.KBFileInfoService import KBFileInfoService

file_info_service = KBFileInfoService()

class KBFileTrunkInfoService:
    def __init__(self, db=None):
        self.__db = db or MySQLInitializer.MySQLHandler()
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()
        self.table_name = "kb_file_trunk_info"

    @staticmethod
    def transform_to_document(trunk: KBFileTrunkInfo) -> Document:
        if isinstance(trunk.kb_id,str):
            trunk.kb_id = int(trunk.kb_id)
        base_metadata = {
            "kb_id": trunk.kb_id,
            "file_id": trunk.file_id,
            "file_trunk_id": trunk.id,
            "source": trunk.meta_data.get("source", ""),
        }

        return Document(
            id=trunk.id,
            page_content=trunk.content,
            metadata=base_metadata
        )

    # 查询数据库数据单条
    def select_by_id(self, id: str) -> KBFileTrunkInfo:
        """根据id查询数据库数据"""
        try:
            query = f"select * from {self.table_name} where id = %s"
            ajax = self.__db.execute_query_one(query, [id])
            return KBFileTrunkInfo(**ajax)
        except Exception as e:
            logger.error(f"Error in select_by_id: {e}")
            raise

    # 查询数据库数据单条
    def select_by_file_id(self, file_id: str) -> List[KBFileTrunkInfo]:
        """根据id查询数据库数据"""
        try:
            query = f"select * from {self.table_name} where file_id = %s and del_flag = 0"
            row_list = self.__db.execute_query(query, [file_id])
            return [KBFileTrunkInfo(**{**row,
                                       'meta_data': eval(row['meta_data']) if isinstance(row['meta_data'], str) else
                                       row['meta_data']}) for row in row_list]
        except Exception as e:
            logger.error(f"Error in select_by_file_id: {e}")
            raise

    def select_by_file_ids(self, file_ids: list[str]) -> List[KBFileTrunkInfo]:
        """根据多个file_id查询数据库数据"""
        try:
            # 使用IN语句查询多个文件
            placeholders = ','.join(['%s'] * len(file_ids))
            query = f"""
                SELECT * 
                FROM {self.table_name} 
                WHERE file_id IN ({placeholders}) 
                AND del_flag = 0
            """
            # 将列表转换为元组参数
            params = tuple(file_ids)

            row_list = self.__db.execute_query(query, params)
            return [KBFileTrunkInfo(**{**row,
                                       'meta_data': json.loads(row['meta_data']) if row['meta_data'] else {}})
                    for row in row_list]
        except Exception as e:
            logger.error(f"Error in select_by_file_ids: {e}")
            raise

    # 构建智能查询条件
    def build_query_conditions(self, params: KBFileTrunkInfo) -> tuple[str, list]:
        """构建智能查询条件"""
        where_clause = []
        params_list = []
        params_dict = params.dict(exclude_unset=True)

        for attr, value in params_dict.items():
            if value is not None:
                # 特殊处理文件名称模糊查询
                if attr == 'content' and '%' in value:
                    where_clause.append(f"{attr} LIKE %s")
                else:
                    where_clause.append(f"{attr} = %s")
                params_list.append(f"{value}")

        return " WHERE " + " AND ".join(where_clause) if where_clause else "", params_list

    # 查询数据库数据
    def select_all(self, params: KBFileTrunkInfo, page_num=None, page_size=None) -> Tuple[List[KBFileTrunkInfo], int]:
        """查询数据库数据"""
        try:
            # 原始查询
            params.del_flag = 0
            where_clause, params_list = self.build_query_conditions(params)
            base_query = f"select * from {self.table_name}" + where_clause + " ORDER BY sort asc"
            # 分页查询
            if page_size is not None and page_num is not None:
                offset = (page_num - 1) * page_size
                data_query = f"{base_query} LIMIT {page_size} OFFSET {offset}"
            else:
                data_query = base_query

            # 总数查询（移除分页）
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # 执行查询
            logger.debug(f"数据查询: {data_query}")
            results = self.__db.execute_query(data_query, params_list)

            logger.debug(f"总数查询: {count_query}")
            total = self.__db.execute_query_one(count_query, params_list)

            processed_results = []
            for row in results:
                # 转换meta_data字段
                if 'meta_data' in row and isinstance(row['meta_data'], str):
                    try:
                        row['meta_data'] = json.loads(row['meta_data'])
                    except json.JSONDecodeError:
                        row['meta_data'] = {}
                        logger.warning(f"Invalid JSON format in meta_data: {row.get('meta_data')}")

                processed_results.append(KBFileTrunkInfo(**row))

            return processed_results, total['COUNT(*)']
        except Exception as e:
            logger.error(f"查询分块列表失败: {str(e)}")
            raise

    # 插入数据库数据
    def insert(self, data: KBFileTrunkInfo) -> int:
        """插入数据库数据"""
        try:
            insert_query, insert_params = self.__db_builder.build_insert(data, self.table_name)
            logger.info(f"insert_query: {insert_query}")
            logger.info(f"insert_params: {insert_params}")
            
            # 更新文件修改时间
            file_info = file_info_service.select_by_id(data.file_id)
            if file_info:
                file_info_service.update(file_info)
            
            return self.__db.execute_non_query(insert_query, insert_params)
        except Exception as e:
            logger.error(f"Error in insert: {e}")
            raise

    # 批量插入数据库数据
    def insert_batch(self, data_list: List[KBFileTrunkInfo]) -> int:
        """批量插入数据库数据"""
        try:
            insert_query, insert_params = self.__db_builder.build_batch_insert(data_list, self.table_name)
            logger.info(f"insert_query: {insert_query}，{insert_params}")
            
            # 更新文件修改时间
            for data in data_list:
                file_info = file_info_service.select_by_id(data.file_id)
                if file_info:
                    file_info_service.update(file_info)
            
            return self.__db.execute_non_query(insert_query, insert_params)
        except Exception as e:
            logger.error(f"Error in insert: {e}")
            raise

    # 删除数据库数据
    def delete(self, id: int) -> int:
        """删除数据库数据"""
        try:
            delete_query = f"update {self.table_name} set del_flag = 1 WHERE id = %s"
            
            # 查询分块信息
            trunk_info = self.select_by_id(id)
            if trunk_info:
                # 更新文件修改时间
                file_info = file_info_service.select_by_id(trunk_info.file_id)
                if file_info:
                    file_info_service.update(file_info)
                
            return self.__db.execute_non_query(delete_query, id)
        except Exception as e:
            logger.error(f"Error in delete: {e}")
            raise

    # 批量删除数据库数据
    def batch_delete(self, ids: str = None, file_ids: str = None, kb_ids: str = None) -> int:
        """批量删除文件记录"""
        try:
            conditions = []
            params = ()
            where_clause = ""

            if ids is not None:
                where_clause = " WHERE "
                if ids is not None:
                    where_clause += f"find_in_set (id,'{ids}')"
                
                # 更新文件修改时间
                for id in ids.split(','):
                    file_info = file_info_service.select_by_id(id)
                    if file_info:
                        file_info_service.update(file_info)

            if file_ids is not None:
                where_clause = " WHERE "
                if file_ids is not None:
                    where_clause += f"find_in_set (file_id,'{file_ids}')"

            if kb_ids is not None:
                where_clause = " WHERE "
                if kb_ids is not None:
                    where_clause += f"find_in_set (kb_id,'{kb_ids}')"

            delete_query = f"delete from {self.table_name} {where_clause}"
            logger.info(f"批量删除SQL: {delete_query}")
            logger.info(f"批量删除参数: {params}")
            return self.__db.execute_non_query(delete_query, params)
        except Exception as e:
            logger.error(f"批量删除失败: {str(e)}")
            raise

    def update(self, data: KBFileTrunkInfo) -> int:
        """更新数据库数据"""
        try:
            update_query, update_params = self.__db_builder.build_update(
                data,
                self.table_name,
                primary_key="id",
                include_empty=["keywords"]
            )
            logger.info(f"update_query: {update_query}")
            logger.info(f"update_params: {update_params}")

            row = self.__db.execute_non_query(update_query, update_params)
            logger.info(f"update row: {row}")
            
            # 更新文件修改时间
            file_info = file_info_service.select_by_id(data.file_id)
            if file_info:
                file_info_service.update(file_info)
            
            return row
        except Exception as e:
            logger.error(f"Error in update: {e}")


    def update_status(self, id: str, status: int) -> int:
        """更新数据库数据"""
        try:
            update_query = f"update {self.table_name} set status = %s WHERE id = %s"
            
            # 查询分块信息
            trunk_info = self.select_by_id(id)
            if trunk_info:
                # 更新文件修改时间
                file_info = file_info_service.select_by_id(trunk_info.file_id)
                if file_info:
                    file_info_service.update(file_info)
            
            return self.__db.execute_non_query(update_query, [status, id])
        except Exception as e:
            logger.error(f"Error in update: {e}")

    def count_by_file_id(self, file_id):
        """根据file_id统计记录数量"""
        try:
            query = f"SELECT COUNT(1) count FROM {self.table_name} WHERE file_id = %s AND del_flag = 0"
            result = self.__db.execute_query(query, [file_id])
            return result[0]['count'] if result else 0
        except Exception as e:
            logger.error(f"Error counting records by file_id: {e}")
        pass

    def get_max_sort(self, file_id):
        """根据file_id获取最大排序值"""
        try:
            query = "SELECT MAX(sort) max_sort FROM kb_file_trunk_info WHERE file_id = %s"
            result = self.__db.execute_query(query, [file_id])
            return result[0]['max_sort'] if result else 0
        except Exception as e:
            logger.error(f"Error getting max sort by file_id: {e}")
        pass
