import json



from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel

from LLM.LLMManager import sys_llm_manager


def preprocess(text: str):
    # 提取关键词
    from Services.KBTrunkVectorService.CustomBM25Retriever import chinese_keywords
    re = chinese_keywords(text)
    print(re)


def search(query: str, kb_ids: list[int]):
    # 测试搜索结果
    from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorRetrivalService
    res = TrunkVectorRetrivalService.hybrid_search(query=query, kb_ids=kb_ids)
    print(res)


def str_to_json(text: str):
    import json
    data_dict = json.loads(text)
    print(data_dict)


def llm_get_keywords(query: str):
    # 测试大模型提取关键词
    from langchain_openai import ChatOpenAI
    from pydantic import SecretStr
    from langchain_core.prompts import ChatPromptTemplate

    llm = ChatOpenAI(base_url="http://172.16.40.213:1444/v1",
                     model="qwen/qwen3-8b",
                     api_key=SecretStr("openai"),
                     max_tokens=5000,
                     temperature=0.3)

    chat_prompt = ChatPromptTemplate.from_messages([
        ("system", """请基于以下文本内容提取20个核心关键词，要求：

            1、关键词需反映文本核心主题和重要实体
            2、优先选择名词性词汇和专业术语
            3、按重要性降序排列
            4、排除常见停用词（如'的'、'是'等）
            5、对同义词进行合并归一化处理
            6、输出格式为JSON数组，如['关键词1', '关键词2', ...]
          """),
        ("human", "{query}")
    ])

    llm = chat_prompt | llm

    res = llm.invoke({"query": query})

    print(res)


def to_json_str():
    from Utils.CommonUtils import to_json_str
    class Student(BaseModel):
        name: str
        age: int

    student = Student(name="Alice", age=20)
    json_str = to_json_str(student)
    print(isinstance(json_str, str))


def is_nltk_downloaded(package_name='punkt'):
    import nltk
    from nltk import downloader
    try:
        # 检查数据包是否存在
        nltk.data.find(f'tokenizers/{package_name}')

        print(nltk.data.path)
        # 检查下载器状态
        dl = downloader.Downloader()
        status = dl.status(package_name)

        return status == 'installed'
    except LookupError:
        return False
    except Exception as e:
        print(f"检测异常: {str(e)}")
        return False


def test_model():
    llm = sys_llm_manager.get_chat_use_llm_helper().get_llm_chat_object(0.3)
    from langchain_core.output_parsers.string import StrOutputParser
    prompt = ChatPromptTemplate.from_messages([
        ("human", "{query}")
    ])
    llm = prompt | llm
    res = llm.invoke({"query": "给我讲一个关于春天的故事"})
    print(res)


def load_file():
    from Services.ContentLoader.LoaderFactory import LoaderFactory
    loader = LoaderFactory.get_file_loader(r"C:\Users\<USER>\Desktop\电子借条合同模板免费1.docx")
    for trunk in loader.load("测试"):
        print(trunk)


def fix_json_quotes(text: str) -> str:
    try:
        fixed_str = text.replace("“", '"').replace("”", '"')
        json.loads(fixed_str)
        return fixed_str
    except json.JSONDecodeError:
        # 如果转换失败，返回原始字符串
        return text

def baidu_search(query: str):
    import requests
    import time
    from bs4 import BeautifulSoup
    params = {'wd': query, "ie": "utf-8","rsv_bp":1,"rsv_idx":1,"fenlei":256,"tn":"baidu"}
    headers = {"connection":"keep-alive","sec-ch-ua":"\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-language":"zh-CN"}
    res = requests.get("http://www.baidu.com/s", params=params, headers=headers, allow_redirects=False)
    html_text = res.text
    print(html_text)
    soup = BeautifulSoup(html_text, 'html.parser')
    links = [a['href'] for a in soup.find_all('a', href=True)]  # 过滤无效标签:ml-citation{ref="1,2" data="citationList"}
    links = [link for link in links if link.startswith('http://www.baidu.com/link?')]
    links = list(set(links))
    links=links[int(len(links)/2):]
    print(links)
    for link in links:
        try:
            time.sleep(2)
            response = requests.get(link, headers=headers)
            soup = BeautifulSoup(response.text, 'html.parser')
            # 获取body内所有文本（包含子标签文本）
            body_text = soup.body.get_text('\n', True)
            if len(body_text) > 1000:
                print(body_text)
                break
        except Exception as e:
            print(f"处理链接 {link} 时出错: {e}")
            continue

def nltk_down():
    import nltk
    from Utils.CommonUtils import get_root_file_path
    path = get_root_file_path("nltk_data")
    nltk.data.path.append(path)
    # nltk.download('punkt',path)
    # nltk.download('punkt_tab',path)
    # nltk.download('averaged_perceptron_tagger',path)
    # nltk.download('averaged_perceptron_tagger_eng',path)

if __name__ == "__main__":
    # text = "一、试点医院实行限时免费停车 1.所有车辆进入试点医院均可免费停放15分钟。 2.持有效就诊（住院）凭证的车辆当日（日以连续24小时计，下同）首个2小时限时免费停车。有效就诊（住院）凭证包括：当日门诊取号单、导诊单、缴费凭条、电子缴费凭证、医疗门诊收费票据纸质或电子版、住院患者预交金收据、出院记录、医疗费用结算单等任意一种相关诊疗凭证（便民挂号凭证除外），须与停车出入场时间有合理相关性。 3.悬挂公安部门核发的新能源汽车号牌的车辆每日首个2小时限时免费。 4.军车、残疾人本人驾驶机动车（残疾人代步车）停车优惠减免按现行有效政策执行。 为加速车辆流转，规范停车秩序，各类优惠减免政策不叠加享受，且同一号牌车辆24小时内限减免一次。 二、试点医院机动车停放服务收费 （一）计费单位 试点医院不区分白天、夜间时段，停车收费以30分钟为计费单位，不足30分钟按30分钟计算；达到单日最高收费标准，以日为计费单位。 （二）停放服务计时收费标准 车辆停放超过限时免费时段后，开始计时收费。为鼓励车辆短停快走，加速车辆流转，计时收费根据停放时长实行累进阶梯递增的收费方式。具体为： 小型车计费停放时长在2小时及以内的，计时收费标准为2元／30分钟，停放时长2小时以上的车辆，计时收费标准为3元／30分钟。中型车、大型车收费标准分别按小型车收费标准的1.5倍、2倍计算。车型分类按国家有关规定执行（下同）。 （三）单日停车最高收费标准 小型车第一日内停放的最高收费标准为50元。连续停放第二日及以上的，单日最高收费标准为60元。中型车、大型车收费标准分别为按小型车收费标准的1.5倍、2倍计算。"
    # preprocess(text)
    # llm_get_keywords(text)
    #search("租赁期限不得超过二十年，超过二十年的，超过部分无效。",[900000])
    #search("出租人出卖租赁房屋的，应当在出卖之前的合理期限内通知承租人，承租人享有以同等条件优先购买的权利；但是，房屋按份共有人行使优先购买权或者出租人将房屋出卖给近亲属的除外。",[900000])
    # str_to_json('{"a":1,"b":2}')
    # to_json_str()
    # test_model()
    # is_nltk_downloaded()
    # load_file()
    # 示例用法
    # json_with_chinese_quotes = '{“name”:“张三”,“age”:30,“address”:{“city”:“北京”,“district”:“朝阳区”}}'
    # fixed_json = fix_json_quotes(json_with_chinese_quotes)
    # print(fixed_json)
    baidu_search("如何办理身份证补办")
    pass
