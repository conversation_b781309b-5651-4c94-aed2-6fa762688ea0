# CSVLoader  可以一行一行加载
# from Models.peewee.OrmModel import KBAnalysisTask, TaskState
import asyncio
import time
from Utils.logs.LoggingConfig import logger

import requests
from bs4 import BeautifulSoup


# from langchain_community.document_loaders import UnstructuredCSVLoader, CSVLoader, PyPDFLoader, \
#     UnstructuredWordDocumentLoader
#
# import re
#
# from langchain_community.vectorstores import FAISS
#
# from Configs.Config import SysConfig
# from LLM.LLMManager import VectorStoreHelper
# from Services.ContentLoader.CsvContentLoader import CsvContentLoader
#
# csv_args = {
#     'delimiter': ',',  # CSV 分隔符，通常为逗号
#     'quotechar': '"',  # 引用字符，通常为双引号
#     'skipinitialspace': True,  # 跳过字段值前后的空白字符（可选）
# }
#
#
# csv=CSVLoader(r'C:\Users\<USER>\Desktop\测试文件\12345传统知识库全量_cleaned.csv',encoding='utf-8',autodetect_encoding=True,csv_args=csv_args)
#
# for document in csv.lazy_load():
#     document.page_content = clean_document_content(document.page_content)
#     print(document)
#
# csv=CsvContentLoader(r'C:\Users\<USER>\Desktop\测试文件\12345传统知识库全量_cleaned.csv')
# for trunks in csv.load():
#     print(trunks)
# import asyncio
#
# async def async_task(item):
#     await asyncio.sleep(1)  # 模拟异步操作
#     print(f"处理完成: {item}")
#
# async def main():
#     items = [1, 2, 3, 4, 5]
#     tasks = [async_task(item) for item in items]
#     await asyncio.gather(*tasks)  # 并发执行所有任务
#
# asyncio.run(main())
# from Services.Retrieval.CustomBM25Retriever import chinese_preprocess
#
# text = "答：提出工伤认定申请应当填写工伤认定申请表，并提交以下材料原件及复印件（留存复印件）： （一）受伤害职工的居民身份证。 （二）劳动、聘用合同文本或者与用人单位存在劳动关系（包括事实劳动关系）、人事关系的其他证明材料； （三）医疗机构出具的职工受伤害时初诊诊断证明书、初诊病历、住院病历，出院记录，或者依法承担职业病诊断的医疗机构出具的职业病诊断证明书（或者职业病诊断鉴定书）； （四）提供两人以上的证人证言及证人身份证明； （五）有下列情况之一的，除提供上述材料外，还应当分别提交相应的证明材料： 1.在工作时间和工作场所内，因履行工作职责受到暴力等意外伤害的，提交公安部门的证明、人民法院的判决书或其他有效证明。 2.因工外出期间，由于工作原因受到伤害或者发生事故下落不明的，提交公安部门的证明或相关部门的证明； 3.上下班途中，受到非本人主要责任的交通事故或者城市轨道交通工具、客运轮渡、火车事故伤害的，提交公安交通管理部门或其他主管行政部门的事故责任认定证明； 4.职工死亡的，提交死亡相关证明； 5.属于抢险救灾等维护国家利益、公共利益活动中受到伤害的，提交民政部门或者其他相关部门的证明； 6.属于因战、因公负伤致残的转业、复员军人，旧伤复发的，提交民政部门颁发的《革命伤残军人证》及劳动能力鉴定机构对旧伤复发的确认证明； 7.近亲属代表伤亡职工提出工伤认定申请或在意见栏中签署意见的，提交有效的近亲属关系证明； 8.申请人为受伤职工所在单位工会组织的，应当提交工会介绍信，经办人身份证明； 9.受伤职工委托代理提出工伤认定申请的，需提交委托代理授权书； 10.用人单位未参加工伤保险的，应当提交营业执照副本或者工商行政管理部门出具的查询证明。 窗口地址：山东省青岛市西海岸新区双珠路166号2号楼4楼 电话：0532-86160357 工作日时间：上午9：00-12:00，下午13:30-17:00"
#
# print(f"{text}--{chinese_preprocess(text)}")
# import nltk
# print(nltk.data.path)
# from Services.ContentLoader.WordDocContentLoader import WordDocContentLoader
#
# doc_loader = WordDocContentLoader(r'C:\Users\<USER>\Desktop\测试文件\5c6fe25f909949e7a6f75d69139d1417.docx')
#
# for trunks in doc_loader.load():
#     print(trunks)
# from langchain_openai import ChatOpenAI
#
# llm = ChatOpenAI(base_url="http://14.22.88.46:4025/v1",model="DeepSeek-R1",api_key="151515")
#
# res= llm.stream("给我讲个故事")
#
# for r in res:
#     print(r)
# FAISS.from_documents(
#     documents=[],  # 先创建空索引
#     embedding=self.__embeddings
# )
# from langchain_ollama import OllamaLLM
# llm = OllamaLLM(base_url="http://172.16.40.90:11434",
#                 model="deepseek-r1:1.5b",
#                 temperature=0.5)
#
# res=llm.invoke("你好")
# print(res)
# from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
# from Services.SqlServer.KBFileInfoService import KBFileInfoService
# from Utils.MinIOClient import MinIOClient
#
# # print(os.path.exists("C:\\work\\pyWord\\hzyp_agents\\Resources\\Law"))
#
# res=ResourceService.get_absolute_path(r"Resource\Law\中华人民共和国民典法.docx")
# res=MinIOClient().upload_file(res)
# print(res)
# # 保持test为同步函数
# def test(content: str):
#     print(content)
#     sum_i=0
#     for i in range(10000):
#         sum_i+=i
#     print(sum_i)
#     return sum_i
#
# async def main():
#     # 使用线程池并行执行
#     loop = asyncio.get_event_loop()
#     tasks = [
#         loop.run_in_executor(None, test, f"任务{i}")
#         for i in range(10)
#     ]
#     await asyncio.gather(*tasks)
#
# asyncio.run(main())
# row_list, total = KBAnalysisTaskServer.query(KBAnalysisTask.state_equals(TaskState.WAITING), page=1,page_size=10)
# print(total)
# for row in row_list:
#     print(row)
# file_url = MinIOClient().upload_file(r"C:\Users\<USER>\Desktop\10.解决方案产品销售合同（中泰是甲方 即销售方）.docx")
# print(file_url)
# KBAnalysisTaskServer.insert_one_task(user_id="1234",file_url=file_url)
# res,total=KBAnalysisTaskServer.query(KBAnalysisTask.state_equals(TaskState.WAITING),is_dic=False)
# print(total)
# for task in res:
#     print(task.task_type)
# res=KBFileInfoService().select_by_kb_id(INTER_LAW_KNOWLEDGE_ID, 2)
# print(res)
# from LLM.LLMManager import sys_llm_manager
# llm_obj=sys_llm_manager.get_chat_use_llm_helper().get_llm_chat_object(0.3)
# res=llm_obj.stream("介绍一下你自己")
# for r in res:
#     print(r)
# llm_obj = llm_helper.get_llm_chat_object(self.__temperature)
# prompt = ChatPromptTemplate.from_messages(
#     [
#         (
#             "system",
#             """你是一个专业的技术顾问，回答必须基于知识库原文，避免推测或外部知识补充。
#             回答指南:
#             1、优先引用：使用知识库原文的完整信息片段。
#             2、自然表述：在准确匹配基础上进行口语化转述。
#             3、明确说明：无匹配信息时清晰告知知识库未覆盖。
#             4、信息整合：必要时允许自然衔接不同段落信息.
#             5、专业态度：保持技术文档的中立性与完整性。"""
#         ),
#         MessagesPlaceholder(variable_name="history"),
#         ("human", "[问题]：\n{input}\n\n[知识库内容]：\n{knowledge_base}\n"),
#     ]
# )
# from langchain_community.chat_message_histories import ChatMessageHistory
# runnable = prompt | llm_obj
#
# conversation = RunnableWithMessageHistory(
#     runnable=runnable,
#     get_session_history=lambda :ChatMessageHistory(),  # 返回整个 self._memory 对象
#     input_messages_key="input",
#     history_messages_key="history",
# )
# config = RunnableConfig(configurable={"session_id":10})
#
# # res= conversation.astream(
# #     input={"knowledge_base": "11", "input": "111"},
# #     config=config
# # )
#
#
# async def main():
#     res = conversation.astream(
#         input={"knowledge_base": "11", "input": "111"},
#         config=config
#     )
#
#     async for r in res:
#         print(r)
#
#
# asyncio.run(main())
# from LLM.LLMManager import sys_llm_manager
#
# from typing import Optional
#
# from pydantic import BaseModel, Field
#
#
# # Pydantic
# class Joke(BaseModel):
#     """Joke to tell user."""
#
#     setup: str = Field(description="The setup of the joke")
#     punchline: str = Field(description="The punchline to the joke")
#     rating: Optional[int] = Field(
#         default=None, description="How funny the joke is, from 1 to 10"
#     )
#
# from pydantic import SecretStr
# llm = ChatOpenAI(base_url="http://*************:1444/v1",
#                          model="qwen/qwen3-8b",
#                          api_key=SecretStr("openai"),
#                          max_tokens=5000,
#                          temperature=0.3)
#
# structured_llm = llm.with_structured_output(Joke)
#
# res=structured_llm.invoke("Tell me a joke about cats")
#
# print(res.model_dump_json())
# from pydantic import BaseModel
# import json
#
# class Item(BaseModel):
#     name: str
#     value: int
#
# data = [Item(name="A", value=1), Item(name="B", value=2)]
#
# # 方法1：直接使用model_dump_json()
# json_str = json.dumps([item.model_dump() for item in data])
#
# print(json_str)
#
# # 方法1：分步解析
# data_list = json.loads(json_str)
# print(data_list)
# items = [Item(**item) for item in data_list]
#
# print(items)
#
def split_resume(text):
    # 教育经历
    education_experience = text.split("教育经历")[1]
    # 截取基本信息
    basic_info = text.split("工作经历")[0]
    # 截取从工作经历到项目经验之间的内容
    work_experience = text.split("工作经历")[1].split("项目经验")[0].split("教育经历")[0]

    # 输出
    result = {
        "basic_info": basic_info,
        "work_experience": work_experience,
        "education_experience": education_experience
    }

    for key, value in result.items():
        logger.error(f"{key}:\n{value}\n")
    return result




# 示例文本
text = """
张**
本周活跃
30岁7年本科离职-随时到岗
负责 web 端 app 端的相关测试工作，单体化安装盘测试、数据库兼容测试、国产化中间件兼容测试、多端兼容测试、客户软件容器兼容测试、插件测试、代码变更测试。
期望职位
青岛测试工程师行业不限6-7K
岗位经验
行政专员/助理 4个月
测试工程师 6年
工作经历
山东城乡发展有限公司行政专员/助理
2024.11 - 2025.02
工作内容:
1、负责员工入职离职审核,公司及员工资料整理归档及会议组织
2、公司网络运维和各部门材料物料管理
会议管理
山东亚微软件股份有限公司测试工程师
2020.04 - 2024.10
工作内容:
1、负责测试系统架构设计、各个模块开发、核心代码等设计及实现
2、搭建和优化测试工具,主导技术难题攻关,提升系统的可扩展性
3、协调系统测试和实施环节,把握进度,提供技术支持
4、使用Selenium,LoadRunner等工具惊醒功能测试和压力测试等
功能测试
性能测试
系统测试
Selenium
LoadRunner
南京唯实科技有限公司测试工程师
2018.09 - 2020.01
工作内容:
1、根据产品需求制定测试计划,设计测试用例,撰写技术文档
2、分析并解决产品测试过程中的bug.积极,不断优化现有项目,并解决技术难点,调优系统
3、配合项目实施人员监控软件性能和用户反馈,为维护提供技术支持
4、使用selenium、postman等工具进行功能测试和接口测试等
功能测试
接口测试
Postman
Selenium
教育经历

青岛理工大学
软件工程
本科
2013 - 2017
卓越工程师计划
专业技能
1.测试工具精通,自动化测试工具:熟练掌握Selenium、Postman、JMeter、LoadRunner等主流测试工具,能够高效完成功能测试、接口测试、性能测试和并发测试等任务工具应用经验,使用Selenium实现Web应用的自动化测试,能够快速编写和维护测试脚本,支持多种浏览器和操作系统环境。,利用Postman进行接口测试,能够验证API的正确性、数据交互的完整性和性能表现,通过JMeter和LoadRunner进行性能测试和并发测试,能够模拟高并发场景,分析系统瓶颈,优化系统性能。,2.编程能力,·Python熟练:精通Python语言,能够快速搭建自动化测试环境,编写高效的测试脚本和工具。具备丰,富的实践经验,能够结合Selenium、Requests等库实现复杂的功能测试和接口测试。,自动化测试框架:熟悉Python的unitest、pytest等测试框架,能够设计和实现结构化的测试代码,提升测试效率和可维护性。,3.缺陷管理与文档,缺陷跟踪工具:熟练使用JIRA、Bugzilla等缺陷跟踪工具,能够高效记录、跟踪和管理缺陷,确保问题及时解决。,文档能力:具备出色的文档编写能力,能够撰写清晰、详细的测试报告、测试计划和测试用例文档,为团队协作和项目管理提供有力支持,4.语言能力,英语:通过大学英语四级(CET-4),具备良好的英语读写能力,能够熟练阅读和理解英文技术文档,撰写英文测试报告。,日语:通过日语能力测试N5,具备基础的日语交流能力,能够适应部分日语工作环境,,5.综合技能,敏捷开发:熟悉敏捷开发流程,能够快速响应需求变更,高效完成测试任务。,团队协作:具备良好的团队合作精神,能够与开发、产品等团队成员紧密协作,共同推动项目进展。,问题解决能力;具备较强的问题分析和解决能力,能够快速定位并解决测试过程中遇到的各种问题。
"""

# 打印结果

if __name__ == "__main__":
    split_resume(text)
