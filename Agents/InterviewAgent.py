from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableLambda
from pydantic import BaseModel, Field

from LLM.LLMManager import sys_llm_manager
from Utils.CommonUtils import remove_think_tags, fix_json_quotes


class InterviewSummaryModel(BaseModel):
    summary: str = Field(default="", description="根据面试者的回答，给出面试评价")
    score: int = Field(default=0, description="得分范围为0-100，0分表示面试评价较差，100分表示面试评价较好")


class InterviewAgent:
    TEMPERATURE = 0.5

    @staticmethod
    def generate_evaluation(content: str) -> dict:
        # 生成面试评价
        llm = sys_llm_manager.get_summary_title_use_llm_helper().get_llm_chat_object(InterviewAgent.TEMPERATURE)

        parser = PydanticOutputParser(pydantic_object=InterviewSummaryModel)

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """你是一个专业的面试评价专家，你需要根据面试者的回答，给出面试评价。
                    回答指南:
                    1、面试评价：根据面试者的回答，给出面试评价。
                    2、专业态度：保持中立性与完整性。
                    
                    严格按照以下格式输出：
                    1、输出时严格检查文本是否符合json格式。
                    2、注意json格式的引号为英文引号。
                    
                    {format_instructions}
                    
                    /no_think"""
                ),
                ("human", "[面试内容]：\n{input}"),
            ]
        ).partial(format_instructions=parser.get_format_instructions())

        chain = (prompt | llm | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                 | RunnableLambda(remove_think_tags)) | RunnableLambda(fix_json_quotes) | parser
        res = chain.invoke({"input": content})
        return res.model_dump()

    @staticmethod
    def generate_evaluation_in_interview(content: str, history: str) -> str:
        # 生成面试评价
        llm = sys_llm_manager.get_summary_title_use_llm_helper().get_llm_chat_object(InterviewAgent.TEMPERATURE)

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """你是一个专业的面试评价专家，你需要根据面试者的回答，给出面试评价。
                    回答指南:
                    1、面试评价：根据面试者的回答，给出面试评价。
                    2、专业态度：保持中立性与完整性。

                    以下是面试历史：
                    {history}
                    
                    /no_think"""
                ),
                ("human", "[面试内容]：\n{input}"),
            ]
        ).partial(history=history)

        chain = (prompt | llm | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                 | RunnableLambda(remove_think_tags))
        res = chain.invoke({"input": content})
        return res
