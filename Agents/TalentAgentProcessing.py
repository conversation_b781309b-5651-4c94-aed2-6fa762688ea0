import re

import spacy
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableLambda
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.ResumeInfo import ResumeInfo
from Models.agent.TalentInfoProcessing import EducationAgentProcessing, WorkExperienceAgentProcessing, \
    TalentAgentProcessing, ProjectExperienceAgentProcessing, TalentScore, ContentTrunk
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger

from LLM.LLMManager import embeddings_manager


# 基本信息
base_system_parser = PydanticOutputParser(pydantic_object=TalentAgentProcessing)
base_system_condition = ("准确识别并按照指定格式填充内容，例如姓名、性别、年龄、学历、邮箱、手机号码、现居地址、"
                         "政治面貌、专业水平、求职意向(期望职位 例如 '青岛|Python'、'杭州|律师' 等)"
                         "、期望薪资、工作经验、期望薪资下限、期望薪资上限等数据。")

# 工作经历
work_parser = PydanticOutputParser(pydantic_object=WorkExperienceAgentProcessing)
work_condition = "准确识别内容，例如公司名称、职位、离职原因、起始年份、截至年份。"

# 教育信息
education_parser = PydanticOutputParser(pydantic_object=EducationAgentProcessing)
education_condition = "准确识别内容，例如学校名称、专业、学历信息、简介。"

# 项目经验
project_parser = PydanticOutputParser(pydantic_object=ProjectExperienceAgentProcessing)
project_condition = "准确识别内容，例如技能列表、项目名称、在项目担任职位、项目描述、职责内容、项目起始日期、项目截至日期。"

# 统一提示词模板
system_template = SystemMessagePromptTemplate.from_template(
    """你是一个专业人事经理，根据用户给出的内容进行提取出相关信息，并严格按照规定格式进行输出。
    转化规则:
        1. 仔细阅读并识别用户提供的文本内容，严格按照字段名称和字段含义来赋值绝对不能填充内容以外的信息。
        2. {condition}
    注意事项:
        1. 确保输出格式中的信息是从给定内容中提取的。
        2. 输出格式必须严格按照规定的格式进行输出,字段驼峰命名绝对不允许变动。
        3. 如果内容中没有对应的信息可以用 "" 或 null。
        4. 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
        5. 对于日期等相关数据按照格式转换为 YYYY-MM 格式,例如 "2025.03" 转换后 "2025-03"。
        6. 对于薪资范围，按照格式转换为 K 格式，例如 "10000-20000" 转换后 "10-20"，"6千-1万" 转换后 6-10。
    输出格式：{format_instructions}
    /no_think
   """
)

score_system_template = PydanticOutputParser(pydantic_object=TalentScore)
# 对指定字段进行评分的提示词模板
score_template = SystemMessagePromptTemplate.from_template(
    """你是一位在人力资源领域拥有多年经验的资深专家，对各类企业和行业的招聘需求有着敏锐的洞察力。
    你具备精准的简历分析能力，能够依据不同的求职岗位和行业标准，快速识别简历中的关键信息和潜在问题。你熟悉各类评分标准和评价体系，能够客观、公正地对简历进行打分，并提供具有建设性的反馈。
    评分规则:
        1. 根据学历和教育经历、工作经验、工作经历、期望薪资信息进行综合评价并进行打分(满分100分)。
        2. 对简历中的技能和项目经验进行详细分析，并给出相应的评价和建议。
        3. 根据附加条件结合简历信息给出一个匹配分(满分100分)。
        4. 对简历进行整体评价，给出一个综合评价。
    注意事项:
        1. 确保输出格式中分值是最多保留一位小数。
        2. 如果内容中没有对应的信息可进行打分，就展示为0分。
        4. 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
    附加条件：{condition}
    输出格式：{format_instructions}
    /no_think
   """
)


class TalentAgentProcessing:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__talent_url = SysConfig["talent"]["talent_url"]
        self.__talent_use = SysConfig["llm"]["talent-use"]

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, out: PydanticOutputParser,
                  template: SystemMessagePromptTemplate, condition: str = None) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_llm_helper(self.__talent_use)
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        human_template = HumanMessagePromptTemplate.from_template(
            "内容：{content}"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            template,
            human_template
        ]).partial(format_instructions=out.get_format_instructions(), condition=condition)

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | out)
        return runnable

    def _formatting(self, content: str, condition: str = None):
        try:
            result = self.file_info_split_resume(content)
            # 基本信息
            talent_info = self._talent_info_analysis(result)
            # 工作经历
            work_experience = self._work_info_analysis(result)
            # 教育经历
            education_experience = self._education_info_analysis(result)
            # 项目经验
            project_experience = self._project_info_analysis(result)
            # 评分
            logger.info(f"开始处理最终评分信息")
            score_info = self.__set_llm(score_system_template, score_template, condition).invoke(
                {"content": content}
            )
            logger.info(f"结束处理最终评分信息")
            return self.__assemble_object(talent_info, work_experience, education_experience, project_experience,
                                          score_info)
        except Exception as e:
            print(f"Error in paserse_resume: {str(e)}")

    def _work_info_analysis(self, result):
        if result.work_experience is None:
            return None
        logger.info(f"开始处理工作经历信息")
        work_experience = self.__set_llm(work_parser, system_template, work_condition).invoke(
            {"content": result.work_experience}
        )
        logger.info(f"结束处理工作经历信息")
        return work_experience

    def _education_info_analysis(self, result):
        if result.education_experience is None:
            return None
        logger.info(f"开始处理教育经历信息")
        education_experience = self.__set_llm(education_parser, system_template, education_condition).invoke(
            {"content": result.education_experience}
        )
        logger.info(f"结束处理教育经历信息")
        return education_experience

    def _talent_info_analysis(self, result):
        if result.basic_info is None:
            return None
        logger.info(f"开始处理个人信息")
        talent_info = self.__set_llm(base_system_parser, system_template, base_system_condition).invoke(
            {"content": result.basic_info}
        )
        logger.info(f"结束处理个人信息")
        return talent_info

    def _project_info_analysis(self, result):
        if result.project_experience is None:
            return None
        logger.info(f"开始处理项目经历信息")
        project_experience = self.__set_llm(project_parser, system_template, project_condition).invoke(
            {"content": result.project_experience}
        )
        logger.info(f"结束处理项目经历信息")
        return project_experience

    # 组装对象
    def __assemble_object(self, personal_info: TalentAgentProcessing = None,
                          work_experience: WorkExperienceAgentProcessing = None,
                          education_experience: EducationAgentProcessing = None,
                          project_experience: ProjectExperienceAgentProcessing = None,
                          score_info: TalentScore = None) -> ResumeInfo:
        # 对象拷贝
        talent_info = ResumeInfo()
        if personal_info is not None:
            talent_info.userName = personal_info.userName
            talent_info.sex = personal_info.sex
            talent_info.age = personal_info.age
            talent_info.email = personal_info.email
            talent_info.phone = personal_info.phone
            talent_info.position = personal_info.position
            talent_info.currentAddress = personal_info.currentAddress
            talent_info.introduction = personal_info.introduction
            talent_info.jobIntent = personal_info.jobIntent
            talent_info.salaryExpectation = personal_info.salaryExpectation
            talent_info.workStatus = personal_info.workStatus
        if education_experience is not None:
            talent_info.education = education_experience.education
            talent_info.professionalLevel = education_experience.professionalLevel
            talent_info.foreignProficiency = education_experience.foreignProficiency
            talent_info.tbEducationInfoList = education_experience.tbEducationInfoList
            talent_info.certificate = education_experience.certificate
        if score_info is not None:
            talent_info.minimumEducationScore = score_info.minimumEducationScore
            talent_info.workExperienceScore = score_info.workExperienceScore
            talent_info.jobHoppingRateScore = score_info.jobHoppingRateScore
            talent_info.salaryRangeScore = score_info.salaryRangeScore
            talent_info.totalScore = score_info.totalScore
        # 写入教育信息和工作经历信息
        if work_experience is not None:
            talent_info.tbWorkExperienceList = work_experience.tbWorkExperienceList
        if project_experience is not None:
            talent_info.skillList = project_experience.skillList
        return talent_info

    def boss_info_split_resume(self, text) -> ContentTrunk:
        # 截取基本信息
        strList = text.split("工作经历")
        basic_info = strList[0]
        # 截取从工作经历到项目经验之间的内容
        work_experience = strList[1].split("项目经验")[0]
        # 项目经历
        project_experience = strList[1].split("教育经历")[0]
        # 教育经历
        education_experience = text.split("教育经历")[1]
        # 输出
        result = ContentTrunk()
        result.basic_info = basic_info
        result.work_experience = work_experience
        result.education_experience = education_experience
        result.project_experience = project_experience
        # 返回
        return result

    def file_info_split_resume(self, text):
        # 截取基本信息
        # 工作经历
        logger.info("开始文件内容划分")
        parser = PydanticOutputParser(pydantic_object=ContentTrunk)
        # 统一提示词模板
        template = SystemMessagePromptTemplate.from_template(
            """你是一位文本结构处理大师，对简历文本内容进行结构化划分，希望通过清晰的分类来提升简历的可读性和专业性，使其更符合招聘方的阅读习惯。
            转化规则:
                1. 你是一位在人力资源领域和文档结构分析方面拥有丰富经验的专家，对各类简历的结构和内容有着深刻的理解和专业的分析能力。
                2. 你擅长将复杂的文本信息进行合理分类和组织，使其更加条理清晰、逻辑严谨。
            注意事项:
                1. 确保输出格式中的信息是从给定内容中提取的。
                2. 输出格式必须严格按照规定的格式进行输出,字段驼峰命名绝对不允许变动。
                3. 如果内容中没有对应的信息可以用 "" 或 null。
                4. 划分应严格按照简历的标准结构进行，确保每个部分的内容准确无误。
                5. 在划分过程中，应保持原文信息的完整性和准确性，避免遗漏或误解。
            输出格式：{format_instructions}
            /no_think
           """
        )
        result = self.__set_llm(parser, template).invoke(
            {"content": text}
        )
        logger.info("结束文件内容划分")
        logger.info(f"文件内容划分结果：{result}")
        return result


