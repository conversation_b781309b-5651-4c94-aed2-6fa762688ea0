import re
from typing import Optional, Dict, Any

import spacy
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableLambda
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.ResumeInfo import ResumeInfo
from Models.agent.TalentInfoProcessing import EducationAgentProcessing, WorkExperienceAgentProcessing, \
    TalentAgentProcessing, ProjectExperienceAgentProcessing, TalentScore, ContentTrunk
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger

from LLM.LLMManager import embeddings_manager


# 基本信息
base_system_parser = PydanticOutputParser(pydantic_object=TalentAgentProcessing)
base_system_condition = ("准确识别并按照指定格式填充内容，例如姓名、性别、年龄、学历、邮箱、手机号码、现居地址、"
                         "政治面貌、专业水平、求职意向(期望职位 例如 '青岛|Python'、'杭州|律师' 等)"
                         "、期望薪资、工作经验、期望薪资下限、期望薪资上限等数据。")

# 工作经历
work_parser = PydanticOutputParser(pydantic_object=WorkExperienceAgentProcessing)
work_condition = "准确识别内容，例如公司名称、职位、离职原因、起始年份、截至年份。"

# 教育信息
education_parser = PydanticOutputParser(pydantic_object=EducationAgentProcessing)
education_condition = "准确识别内容，例如学校名称、专业、学历信息、简介。"

# 项目经验
project_parser = PydanticOutputParser(pydantic_object=ProjectExperienceAgentProcessing)
project_condition = "准确识别内容，例如技能列表、项目名称、在项目担任职位、项目描述、职责内容、项目起始日期、项目截至日期。"

# 统一提示词模板
system_template = SystemMessagePromptTemplate.from_template(
    """你是一个专业人事经理，根据用户给出的内容进行提取出相关信息，并严格按照规定格式进行输出。
    转化规则:
        1. 仔细阅读并识别用户提供的文本内容，严格按照字段名称和字段含义来赋值绝对不能填充内容以外的信息。
        2. {condition}
    注意事项:
        1. 确保输出格式中的信息是从给定内容中提取的。
        2. 输出格式必须严格按照规定的格式进行输出,字段驼峰命名绝对不允许变动。
        3. 如果内容中没有对应的信息可以用 "" 或 null。
        4. 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
        5. 对于日期等相关数据按照格式转换为 YYYY-MM 格式,例如 "2025.03" 转换后 "2025-03"。
        6. 对于薪资范围，按照格式转换为 K 格式，例如 "10000-20000" 转换后 "10-20"，"6千-1万" 转换后 6-10。
    输出格式：{format_instructions}
    /no_think
   """
)

score_system_template = PydanticOutputParser(pydantic_object=TalentScore)
# 对指定字段进行评分的提示词模板
score_template = SystemMessagePromptTemplate.from_template(
    """你是一位在人力资源领域拥有多年经验的资深专家，对各类企业和行业的招聘需求有着敏锐的洞察力。
    你具备精准的简历分析能力，能够依据不同的求职岗位和行业标准，快速识别简历中的关键信息和潜在问题。你熟悉各类评分标准和评价体系，能够客观、公正地对简历进行打分，并提供具有建设性的反馈。
    评分规则:
        1. 根据学历和教育经历、工作经验、工作经历、期望薪资信息进行综合评价并进行打分(满分100分)。
        2. 对简历中的技能和项目经验进行详细分析，并给出相应的评价和建议。
        3. 根据附加条件结合简历信息给出一个匹配分(满分100分)。
        4. 对简历进行整体评价，给出一个综合评价。
    注意事项:
        1. 确保输出格式中分值是最多保留一位小数。
        2. 如果内容中没有对应的信息可进行打分，就展示为0分。
        4. 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
    附加条件：{condition}
    输出格式：{format_instructions}
    /no_think
   """
)


class TalentAgentProcessing:
    """
    人才简历处理代理类
    负责简历内容的解析、分块和信息提取
    """
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        """初始化人才处理代理"""
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__talent_url = SysConfig["talent"]["talent_url"]
        self.__talent_use = SysConfig["llm"]["talent-use"]

    def __set_llm(self, out: PydanticOutputParser,
                  template: SystemMessagePromptTemplate, condition: str = None) -> RunnableWithMessageHistory | None:
        """
        根据LLM的名字设置LLM相关信息，包含对话信息

        Args:
            out: Pydantic输出解析器
            template: 系统消息提示模板
            condition: 附加条件

        Returns:
            配置好的可运行对象或None
        """
        llm_helper = sys_llm_manager.get_llm_helper(self.__talent_use)
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        human_template = HumanMessagePromptTemplate.from_template(
            "内容：{content}"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            template,
            human_template
        ]).partial(format_instructions=out.get_format_instructions(), condition=condition)

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | out)
        return runnable

    def _formatting(self, content: str, condition: str = None) -> Optional[ResumeInfo]:
        """
        格式化简历内容，提取各部分信息并组装成完整的简历对象

        Args:
            content: 原始简历文本内容
            condition: 评分条件

        Returns:
            处理后的简历信息对象或None
        """
        try:
            # 智能分块处理
            result = self.intelligent_resume_segmentation(content)

            # 并行处理各部分信息
            talent_info = self._extract_basic_info(result)
            work_experience = self._extract_work_experience(result)
            education_experience = self._extract_education_info(result)
            project_experience = self._extract_project_experience(result)

            # 评分处理
            logger.info("开始处理最终评分信息")
            score_info = self._calculate_resume_score(content, condition)
            logger.info("结束处理最终评分信息")

            return self._assemble_resume_object(talent_info, work_experience, education_experience,
                                              project_experience, score_info)
        except Exception as e:
            logger.error(f"简历格式化处理失败: {str(e)}")
            return None

    def _extract_work_experience(self, result: ContentTrunk) -> Optional[WorkExperienceAgentProcessing]:
        """
        提取工作经历信息

        Args:
            result: 分块后的简历内容

        Returns:
            工作经历信息对象或None
        """
        if not result.work_experience:
            return None

        logger.info("开始处理工作经历信息")
        try:
            work_experience = self.__set_llm(work_parser, system_template, work_condition).invoke(
                {"content": result.work_experience}
            )
            logger.info("工作经历信息处理完成")
            return work_experience
        except Exception as e:
            logger.error(f"工作经历信息处理失败: {str(e)}")
            return None

    def _extract_education_info(self, result: ContentTrunk) -> Optional[EducationAgentProcessing]:
        """
        提取教育经历信息

        Args:
            result: 分块后的简历内容

        Returns:
            教育经历信息对象或None
        """
        if not result.education_experience:
            return None

        logger.info("开始处理教育经历信息")
        try:
            education_experience = self.__set_llm(education_parser, system_template, education_condition).invoke(
                {"content": result.education_experience}
            )
            logger.info("教育经历信息处理完成")
            return education_experience
        except Exception as e:
            logger.error(f"教育经历信息处理失败: {str(e)}")
            return None

    def _extract_basic_info(self, result: ContentTrunk) -> Optional[TalentAgentProcessing]:
        """
        提取基本个人信息

        Args:
            result: 分块后的简历内容

        Returns:
            基本信息对象或None
        """
        if not result.basic_info:
            return None

        logger.info("开始处理个人基本信息")
        try:
            talent_info = self.__set_llm(base_system_parser, system_template, base_system_condition).invoke(
                {"content": result.basic_info}
            )
            logger.info("个人基本信息处理完成")
            return talent_info
        except Exception as e:
            logger.error(f"个人基本信息处理失败: {str(e)}")
            return None

    def _extract_project_experience(self, result: ContentTrunk) -> Optional[ProjectExperienceAgentProcessing]:
        """
        提取项目经历信息

        Args:
            result: 分块后的简历内容

        Returns:
            项目经历信息对象或None
        """
        if not result.project_experience:
            return None

        logger.info("开始处理项目经历信息")
        try:
            project_experience = self.__set_llm(project_parser, system_template, project_condition).invoke(
                {"content": result.project_experience}
            )
            logger.info("项目经历信息处理完成")
            return project_experience
        except Exception as e:
            logger.error(f"项目经历信息处理失败: {str(e)}")
            return None

    def _calculate_resume_score(self, content: str, condition: str = None) -> Optional[TalentScore]:
        """
        计算简历评分

        Args:
            content: 原始简历内容
            condition: 评分条件

        Returns:
            评分信息对象或None
        """
        try:
            score_info = self.__set_llm(score_system_template, score_template, condition).invoke(
                {"content": content}
            )
            return score_info
        except Exception as e:
            logger.error(f"简历评分计算失败: {str(e)}")
            return None

    def _assemble_resume_object(self, personal_info: Optional[TalentAgentProcessing] = None,
                               work_experience: Optional[WorkExperienceAgentProcessing] = None,
                               education_experience: Optional[EducationAgentProcessing] = None,
                               project_experience: Optional[ProjectExperienceAgentProcessing] = None,
                               score_info: Optional[TalentScore] = None) -> ResumeInfo:
        """
        组装完整的简历信息对象

        Args:
            personal_info: 个人基本信息
            work_experience: 工作经历信息
            education_experience: 教育经历信息
            project_experience: 项目经历信息
            score_info: 评分信息

        Returns:
            完整的简历信息对象
        """
        talent_info = ResumeInfo()

        # 填充个人基本信息
        if personal_info:
            self._fill_personal_info(talent_info, personal_info)

        # 填充教育信息
        if education_experience:
            self._fill_education_info(talent_info, education_experience)

        # 填充评分信息
        if score_info:
            self._fill_score_info(talent_info, score_info)

        # 填充工作经历信息
        if work_experience:
            talent_info.tbWorkExperienceList = work_experience.tbWorkExperienceList

        # 填充项目经历信息
        if project_experience:
            talent_info.skillList = project_experience.skillList

        return talent_info

    def _fill_personal_info(self, talent_info: ResumeInfo, personal_info: TalentAgentProcessing) -> None:
        """填充个人基本信息"""
        talent_info.userName = personal_info.userName
        talent_info.sex = personal_info.sex
        talent_info.age = personal_info.age
        talent_info.email = personal_info.email
        talent_info.phone = personal_info.phone
        talent_info.position = personal_info.position
        talent_info.currentAddress = personal_info.currentAddress
        talent_info.introduction = personal_info.introduction
        talent_info.jobIntent = personal_info.jobIntent
        talent_info.salaryExpectation = personal_info.salaryExpectation
        talent_info.workStatus = personal_info.workStatus

    def _fill_education_info(self, talent_info: ResumeInfo, education_experience: EducationAgentProcessing) -> None:
        """填充教育信息"""
        talent_info.education = education_experience.education
        talent_info.professionalLevel = education_experience.professionalLevel
        talent_info.foreignProficiency = education_experience.foreignProficiency
        talent_info.tbEducationInfoList = education_experience.tbEducationInfoList
        talent_info.certificate = education_experience.certificate

    def _fill_score_info(self, talent_info: ResumeInfo, score_info: TalentScore) -> None:
        """填充评分信息"""
        talent_info.minimumEducationScore = score_info.minimumEducationScore
        talent_info.workExperienceScore = score_info.workExperienceScore
        talent_info.jobHoppingRateScore = score_info.jobHoppingRateScore
        talent_info.salaryRangeScore = score_info.salaryRangeScore
        talent_info.totalScore = score_info.totalScore

    def intelligent_resume_segmentation(self, text: str) -> ContentTrunk:
        """
        智能简历分块方法 - 使用LLM进行内容识别和分块
        能够处理不同格式的简历，智能识别各部分内容

        Args:
            text: 原始简历文本

        Returns:
            分块后的简历内容对象
        """
        logger.info("开始智能简历内容分块")

        try:
            # 首先尝试使用LLM进行智能分块
            result = self._llm_based_segmentation(text)

            # 如果LLM分块失败，回退到规则分块
            if not self._validate_segmentation_result(result):
                logger.warning("LLM分块结果不完整，回退到规则分块")
                result = self._rule_based_segmentation(text)

            logger.info("简历内容分块完成")
            logger.debug(f"分块结果: {result}")
            return result

        except Exception as e:
            logger.error(f"智能分块失败: {str(e)}, 使用规则分块")
            return self._rule_based_segmentation(text)

    def _llm_based_segmentation(self, text: str) -> ContentTrunk:
        """使用LLM进行智能分块"""
        parser = PydanticOutputParser(pydantic_object=ContentTrunk)
        template = SystemMessagePromptTemplate.from_template(
            """你是一位专业的简历结构分析专家，能够准确识别和分类简历中的不同内容模块。

            任务目标：
            将给定的简历文本智能分割为四个主要部分：基本信息、工作经历、教育经历、项目经历。

            分析规则：
            1. 基本信息(basic_info)：包含姓名、性别、年龄、联系方式、求职意向、个人简介等个人基础信息
            2. 工作经历(work_experience)：包含所有工作履历、职位、公司名称、工作时间、工作内容等
            3. 教育经历(education_experience)：包含学历、学校、专业、毕业时间、证书等教育相关信息
            4. 项目经历(project_experience)：包含参与的项目、项目描述、技能、职责等项目相关信息

            智能识别要点：
            - 即使内容分布在不同位置，也要准确归类到对应模块
            - 如果某个模块的内容分散在多个地方，请合并到一起
            - 保持原文信息的完整性，不要遗漏重要内容
            - 如果某个模块没有相关内容，设置为空字符串

            注意事项：
            1. 输出必须是有效的JSON格式
            2. 字段名称必须严格按照驼峰命名规范
            3. 确保内容分类准确，避免交叉污染

            输出格式：{format_instructions}
            /no_think
            """
        )

        return self.__set_llm(parser, template).invoke({"content": text})

    def _rule_based_segmentation(self, text: str) -> ContentTrunk:
        """基于规则的高效分块方法"""
        result = ContentTrunk()

        # 使用更全面的关键词和模式匹配
        result = self._enhanced_rule_segmentation(text)

        # 如果规则分块效果不好，尝试智能合并
        if not self._validate_segmentation_result(result):
            result = self._smart_content_merge(text, result)

        return result

    def _enhanced_rule_segmentation(self, text: str) -> ContentTrunk:
        """增强的规则分块方法"""
        result = ContentTrunk()

        # 预处理文本
        normalized_text = self._normalize_text(text)
        lines = normalized_text.split('\n')

        # 定义更全面的关键词模式
        patterns = {
            'work': [
                r'工作经[历验]', r'职业经历', r'任职经历', r'工作履历', r'从业经历',
                r'工作背景', r'职场经历', r'就职经历', r'employment', r'work\s*experience'
            ],
            'education': [
                r'教育经历', r'教育背景', r'学习经历', r'教育信息', r'学历信息',
                r'毕业院校', r'就读经历', r'education', r'academic'
            ],
            'project': [
                r'项目经[历验]', r'项目背景', r'参与项目', r'项目介绍', r'项目展示',
                r'作品展示', r'project', r'portfolio'
            ],
            'skill': [
                r'技能', r'专业技能', r'核心技能', r'技术栈', r'掌握技术',
                r'skill', r'technical'
            ]
        }

        # 找到各部分的边界
        section_boundaries = self._find_section_boundaries(lines, patterns)

        # 根据边界分割内容
        result = self._split_content_by_boundaries(lines, section_boundaries)

        return result

    def boss_info_split_resume(self, text: str) -> ContentTrunk:
        """
        Boss直聘格式的简历分块方法（保留兼容性）

        Args:
            text: Boss直聘格式的简历文本

        Returns:
            分块后的简历内容对象
        """
        logger.warning("使用旧版Boss分块方法，建议使用intelligent_resume_segmentation")
        return self._simple_split_resume(text)

    def _validate_segmentation_result(self, result: ContentTrunk) -> bool:
        """
        验证分块结果的有效性

        Args:
            result: 分块结果

        Returns:
            是否有效
        """
        if not result:
            return False

        # 检查是否至少有基本信息
        if not result.basic_info or len(result.basic_info.strip()) < 10:
            return False

        # 检查是否有任何一个主要部分有内容
        has_content = any([
            result.work_experience and len(result.work_experience.strip()) > 0,
            result.education_experience and len(result.education_experience.strip()) > 0,
            result.project_experience and len(result.project_experience.strip()) > 0
        ])

        return has_content

    def _find_section_positions(self, text: str, section_keywords: Dict[str, list]) -> Dict[str, int]:
        """
        查找各个部分在文本中的位置

        Args:
            text: 简历文本
            section_keywords: 各部分的关键词

        Returns:
            各部分的位置字典
        """
        positions = {}
        text_lower = text.lower()

        for section, keywords in section_keywords.items():
            for keyword in keywords:
                pos = text_lower.find(keyword)
                if pos != -1:
                    positions[section] = pos
                    break

        return positions

    def _split_by_positions(self, text: str, sections: Dict[str, int]) -> ContentTrunk:
        """
        根据位置信息分割文本

        Args:
            text: 简历文本
            sections: 各部分位置

        Returns:
            分块结果
        """
        result = ContentTrunk()

        # 按位置排序
        sorted_sections = sorted(sections.items(), key=lambda x: x[1])

        # 基本信息是第一个部分之前的内容
        if sorted_sections:
            result.basic_info = text[:sorted_sections[0][1]].strip()
        else:
            result.basic_info = text
            return result

        # 分割各个部分
        for i, (section_name, pos) in enumerate(sorted_sections):
            start_pos = pos
            end_pos = sorted_sections[i + 1][1] if i + 1 < len(sorted_sections) else len(text)

            content = text[start_pos:end_pos].strip()

            if section_name == 'work':
                result.work_experience = content
            elif section_name == 'education':
                result.education_experience = content
            elif section_name == 'project':
                result.project_experience = content

        return result

    def _simple_split_resume(self, text: str) -> ContentTrunk:
        """
        简单的分割方法（最后的备用方案）

        Args:
            text: 简历文本

        Returns:
            分块结果
        """
        result = ContentTrunk()

        try:
            # 尝试按"工作经历"分割
            if "工作经历" in text:
                parts = text.split("工作经历", 1)
                result.basic_info = parts[0].strip()
                remaining = parts[1] if len(parts) > 1 else ""

                # 继续分割其他部分
                if "项目经验" in remaining:
                    work_parts = remaining.split("项目经验", 1)
                    result.work_experience = ("工作经历" + work_parts[0]).strip()
                    remaining = work_parts[1] if len(work_parts) > 1 else ""

                if "教育经历" in remaining:
                    edu_parts = remaining.split("教育经历", 1)
                    if not result.work_experience:
                        result.work_experience = ("工作经历" + edu_parts[0]).strip()
                    result.education_experience = ("教育经历" + edu_parts[1]).strip() if len(edu_parts) > 1 else ""
            else:
                # 如果没有明确的分割标识，将所有内容作为基本信息
                result.basic_info = text

        except Exception as e:
            logger.error(f"简单分割失败: {str(e)}")
            result.basic_info = text

        return result

    def file_info_split_resume(self, text: str) -> ContentTrunk:
        """
        文件信息分割方法（保留兼容性，推荐使用intelligent_resume_segmentation）

        Args:
            text: 简历文本

        Returns:
            分块结果
        """
        logger.warning("使用旧版文件分割方法，建议使用intelligent_resume_segmentation")
        return self.intelligent_resume_segmentation(text)


