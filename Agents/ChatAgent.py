import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, List

import tiktoken
import random
from fastapi.responses import StreamingResponse
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, ToolMessage, SystemMessage
from langchain_core.messages import AIMessageChunk
from langchain_core.runnables import RunnableWithMessageHistory, RunnableConfig

from LLM import BaseLLMHelper
from LLM.LLMManager import sys_llm_manager
from Models.pojo.KBConversationDetail import KBConversationDetail
from Services.ConversationMemory import conversation_memory_manager
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger




@dataclass
class ChatPreTaskResult:
    result: bool = False,
    args: Any = None,
    message: str = ""


class ChatAgent(ABC):
    _llm_name: str | None = "DEFAULT"

    @abstractmethod
    def __init__(self, conversation_id: str, group_id: str = None):
        self._conversation_id = conversation_id
        self.group_id = group_id
        self.stream_stop = False  # 用于中断当前会话

        self._db_manager = conversation_memory_manager
        self._memory = self._loadConversationMemory()
        self._conversation: RunnableWithMessageHistory = self.__set_llm()

    @staticmethod
    def str_token_counter(text: str) -> int:
        #enc = tiktoken.get_encoding("gpt2")
        return len(text)

    @staticmethod
    def tiktoken_counter(messages: List[BaseMessage]) -> int:
        num_tokens = 3
        tokens_per_message = 3
        tokens_per_name = 1
        for msg in messages:
            if isinstance(msg, HumanMessage):
                role = "user"
            elif isinstance(msg, AIMessage):
                role = "assistant"
            elif isinstance(msg, ToolMessage):
                role = "tool"
            elif isinstance(msg, SystemMessage):
                role = "system"
            else:
                raise ValueError(f"Unsupported messages type {msg.__class__}")
            num_tokens += (
                    tokens_per_message
                    + ChatAgent.str_token_counter(role)
                    + ChatAgent.str_token_counter(msg.content)
            )
            if msg.name:
                num_tokens += tokens_per_name + ChatAgent.str_token_counter(msg.name)
        return num_tokens

    @property
    def conversation_id(self) -> str:
        return self._conversation_id

    def switch_llm(self, llm_name: str = None, model_think_mode: str = None) -> bool:
        if self._llm_name == llm_name:
            return True

        conversation = self.__set_llm(llm_name, model_think_mode)
        if conversation is None:
            return False

        self._conversation = conversation
        return True

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, llm_name: str = None, model_think_mode: str = None) -> RunnableWithMessageHistory | None:
        if llm_name is None:
            llm_helper = sys_llm_manager.get_chat_use_llm_helper()
        else:
            llm_helper = sys_llm_manager.get_llm_helper(llm_name)

        if llm_helper is None:
            return None

        think_mode_str = llm_helper.get_think_mode_str(model_think_mode)

        conversation = self._create_conversation_obj(llm_helper, think_mode_str)
        self._llm_name = llm_name
        return conversation

    def _loadConversationMemory(self) -> ChatMessageHistory:
        """加载对话历史"""
        memory = ChatMessageHistory()
        try:
            data_list, total = self._db_manager.query_conversation_details(user_flag=self.group_id,
                                                                           conversation_id=self.conversation_id)
            # 只取最后6条数据
            for data in data_list[-6:]:
                if data.message_type == "human":
                    memory.add_user_message(data.content)
                else:
                    memory.add_ai_message(data.content)
        except Exception as e:
            print(f"Error loading conversation memory: {e}")

        return memory

    async def _saveConversationMemory(self, user_input, response_content):
        """保存对话历史到文件"""
        try:
            logger.info(f"保存对话历史到文件：{self.conversation_id},group_id:{self.group_id}")
            conversation_list = [
                KBConversationDetail(conversation_id=self.conversation_id,
                                     message_type="human",
                                     content=user_input,
                                     message_time=datetime.now()),
                KBConversationDetail(conversation_id=self.conversation_id,
                                     message_type="ai",
                                     content=response_content,
                                     message_time=datetime.now())
            ]
            for conversation in conversation_list:
                logger.info(f"保存对话历史到文件：{conversation}")
                self._db_manager.new_memory(memory=conversation, user_flag=self.group_id)
        except Exception as e:
            logger.error(f"Error saving conversation memory: {e}")

    def get_messages(self) -> list:
        return self._memory.messages

    @abstractmethod
    def _create_conversation_obj(self, llm_helper: BaseLLMHelper, think_mode_str: str) -> RunnableWithMessageHistory:
        pass

    @abstractmethod
    def _pre_chat(self, message: str) -> ChatPreTaskResult:
        pass

    @abstractmethod
    def _generate_conversation_input(self, input: str, args: Any) -> {}:
        pass

    async def chat_for_answer(self, user_input: str):
        try:
            # 重置停止标志，确保新对话可以正常进行
            self.stream_stop = False

            pre_result = self._pre_chat(user_input)

            if not pre_result.result:
                await self._saveConversationMemory(user_input, pre_result.message)
                yield pre_result.message
                return

            config = RunnableConfig(configurable={"session_id": self.conversation_id})

            response_stream = self._conversation.astream(
                input=self._generate_conversation_input(user_input, pre_result.args),
                config=config
            )
            if response_stream is None:
                return

            response_content = ""
            async for chunk in response_stream:
                if chunk is not None:
                    if isinstance(chunk, AIMessageChunk):
                        chunk = str(chunk.content)
                    if isinstance(chunk, (dict, list)):
                        # 如果是字典或列表，转换为字符串
                        chunk = str(chunk)

                    response_content += chunk

                    # 检查是否需要中断当前会话
                    if self.stream_stop:
                        break
                    yield chunk

            # 上下文中，将think去掉
            for message in self._memory.messages:
                if isinstance(message, AIMessage):
                    message.content = remove_think_tags(message.content)
            # 使用正确的方法保存对话历史da
            if response_content:
                # 异步方法引用另一个异步方法增加需要标识await关键字等待
                await self._saveConversationMemory(user_input, response_content)
        except Exception as e:
            print(f"Error in streaming response: {str(e)}")
            # 从异常消息数组中随机选择一个提示
            random_message = error_messages[random.randint(0, len(error_messages) - 1)]
            yield random_message
        pass

    # 停止回复
    def stop_stream(self):
        self.stream_stop = True
        pass

    async def stream_response(self, message: str):
        """
        使用 SSE 格式的流式响应接口
        :param message: 用户输入的消息
        :return: StreamingResponse 对象
        """
        # 重置停止标志，确保新流式响应可以正常进行
        self.stream_stop = False

        async def generate():
            try:
                async for token in self.chat_for_answer(message):
                    if token:
                        data = json.dumps({
                            'content': token,
                            'end': False
                        }, ensure_ascii=False)
                        yield f"data: {data}\n\n"

            except Exception as e:
                print(f"Stream response error: {str(e)}")
                data = json.dumps({
                    'error': str(e),
                    'end': True
                }, ensure_ascii=False)
                yield f"data: {data}\n\n"

            else:
                # 正常结束时发送结束标记
                data = json.dumps({
                    'content': '',
                    'end': True
                }, ensure_ascii=False)
                yield f"data: {data}\n\n"

        return StreamingResponse(
            generate(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
