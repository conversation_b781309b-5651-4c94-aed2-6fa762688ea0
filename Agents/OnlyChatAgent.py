from typing import Any

from langchain_core.messages import trim_messages
from langchain_core.prompts import ChatPromptTemplate, \
    MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory

from Agents.ChatAgent import ChatAgent, ChatPreTaskResult
from Configs.Config import SysConfig
from LLM.BaseLLMHelper import BaseLLMHelper


class OnlyChatAgent(ChatAgent):

    def __init__(self, conversation_id: str, group_id: str = None):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        super().__init__(conversation_id, group_id)

    def _create_conversation_obj(self, llm_helper: BaseLLMHelper, think_mode_str: str) -> RunnableWithMessageHistory:

        llm_obj = llm_helper.get_llm_chat_object(self.__temperature)

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """你是一个专业的问答助手，请用通俗易懂的语言回答用户问题。
                    {think}"""
                ),
                MessagesPlaceholder(variable_name="history"),
                ("human", "{input}"),
            ]
        ).partial(think=think_mode_str)

        trimmer = trim_messages(
            max_tokens=llm_helper.get_max_tokens(),
            include_system=True,
            strategy="last",
            token_counter=ChatAgent.tiktoken_counter,
            allow_partial=True
        )

        runnable = prompt | trimmer | llm_obj

        conversation = RunnableWithMessageHistory(
            runnable=runnable,
            get_session_history=lambda: self._memory,  # 返回整个 self._memory 对象
            input_messages_key="input",
            history_messages_key="history",
        )
        return conversation

    def _pre_chat(self, user_input: str) -> ChatPreTaskResult:
        return ChatPreTaskResult(result=True)

    def _generate_conversation_input(self, input: str, args: Any) -> {}:
        try:
            return {"input": input}
        except Exception as e:
            print(f"Error in chat_for_answer: {str(e)}")
            return None
