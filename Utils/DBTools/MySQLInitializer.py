import mysql.connector
from mysql.connector import Error

from Configs.Config import SysConfig


class MySQLHandler:
    def __init__(self):
        """
        初始化数据库连接
        """
        self.host = SysConfig["mysql"]["host"]
        self.user = SysConfig["mysql"]["user"]
        self.password = SysConfig["mysql"]["password"]
        self.database = SysConfig["mysql"]["database"]
        self.connection = None
        self.cursor = None
        self.connect()

    def connect(self):
        """
        连接到 MySQL 数据库
        """
        try:
            self.connection = mysql.connector.connect(
                use_pure=True,
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
        except Error as e:
            print(f"连接数据库失败: {e}")

    def execute_query(self, query: str, params=None) -> list[dict]:
        """
        执行 SQL 查询
        """
        if not self.connection.is_connected():
            self.connect()
        try:
            if isinstance(params, str):
                self.cursor.execute(query, (params,))
            else:
                self.cursor.execute(query, params)
            #print("查询执行成功！")
            # 打印执行的sql
            #print(f"执行的SQL: {self.cursor.statement}")
            # 确保读取所有结果
            results = self.cursor.fetchall()
            self.connection.commit()
            return results
        except Error as e:
            print(f"执行查询失败: {e}")
            self.connection.rollback()
            return []

    def execute_query_one(self, query: str, params=None) -> dict:
        """
        执行SQL查询并返回单个结果
        :param query: SQL查询语句
        :param params: 查询参数
        :return: 单个查询结果，如果查询失败或没有结果则返回None
        """
        if not self.connection.is_connected():
            self.connect()
        try:
            if isinstance(params, str):
                self.cursor.execute(query, (params,))
            else:
                self.cursor.execute(query, params)
            #print(f"执行的SQL: {self.cursor.statement}")
            result = self.cursor.fetchone()
            self.connection.commit()
            return result
        except Error as e:
            print(f"执行查询失败: {e}")
            self.connection.rollback()
            return None

    def execute_non_query(self, query: str, params=None):
        """
        执行非查询操作（如插入、更新、删除）
        """
        if not self.connection.is_connected():
            self.connect()
        try:
            if isinstance(params, str):
                self.cursor.execute(query, (params,))
            else:
                self.cursor.execute(query, params)
            self.connection.commit()
            print("操作执行成功！")
            return self.cursor.rowcount
        except Error as e:
            print(f"执行操作失败: {e}")
            self.connection.rollback()
            return -1

    def execute_sql_file(self, file_path: str):
        """
        执行SQL文件中的语句
        :param file_path: SQL文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_commands = f.read().split(';')
                for command in sql_commands:
                    if command.strip():
                        self.execute_non_query(command)
            print(f"成功执行SQL文件: {file_path}")
        except Exception as e:
            print(f"执行SQL文件失败: {e}")

    def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        :param table_name: 表名
        :return: 布尔值，表示表是否存在
        """
        query = """
            SELECT COUNT(*) AS count
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = %s
        """
        result = self.execute_query(query, (self.database, table_name))
        return bool(result and result[0]['count'] > 0)

    def close(self):
        """
        关闭数据库连接
        """
        if self.cursor:
            self.cursor.close()
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("数据库连接已关闭！")

