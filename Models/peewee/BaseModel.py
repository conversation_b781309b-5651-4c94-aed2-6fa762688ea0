from datetime import datetime
from enum import Enum

from peewee import *
from Models.peewee.pool import PooledMySQLDatabase  # 这里编译器会报错，但是实际运行不会报错
from Models.peewee.migrate import SchemaMigrator, migrate
from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger

host = SysConfig["mysql"]["host"]
user = SysConfig["mysql"]["user"]
password = SysConfig["mysql"]["password"]
database = SysConfig["mysql"]["database"]

from peewee import TextField


class MediumTextField(TextField):
    field_type = 'MEDIUMTEXT'


class RobustPooledMySQLDatabase(PooledMySQLDatabase):
    def execute_sql(self, sql, params=None, commit=True):
        try:
            # 添加连接有效性检查
            if not self.is_connection_usable():
                self.close()
                self.connect()
            return super().execute_sql(sql, params, commit)
        except (InterfaceError, OperationalError) as e:
            #logger.warning(f"Connection error, reconnecting: {str(e)}")
            self.close()
            self.connect()
            return super().execute_sql(sql, params, commit)
        except Exception as e:
            logger.error(f"SQL Error: {str(e)}")
            raise


mysql_db = RobustPooledMySQLDatabase(database, user=user, password=password,
                                     host=host, port=3306, max_connections=None, stale_timeout=300)

migrator = SchemaMigrator(mysql_db)

try:
    with mysql_db.atomic():
        migrate(
            #迁移表结构-示例
            #migrator.add_column('kb_analysis_group', 'test', TextField(null=True))
        )
except Exception as e:
    pass


class BaseModel(Model):
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    extra = TextField(null=True)

    class Meta:
        database = mysql_db
        legacy_table_names = False

    def save(self, *args, **kwargs):
        # 每次保存时更新 update_time
        self.update_time = datetime.now()
        return super().save(*args, **kwargs)

    @classmethod
    def cus_insert_many(cls, data: list):
        try:
            with mysql_db.atomic():
                cls.insert_many(data).execute()
        except IntegrityError as e:
            logger.error(f"插入数据时发生错误: {e}")
            return 0
        return 1

    @classmethod
    def cus_dict_update_by_id(cls, id, data: dict):
        try:
            query_one = cls.get_by_id(id)
            if not query_one:
                return 0
            # 动态赋值
            for field, value in data.items():
                if hasattr(query_one, field):
                    setattr(query_one, field, value)
            return query_one.save()
        except Exception as e:
            logger.error(f"更新数据时发生错误: {e}")
            return 0

    @classmethod
    def cus_query_page(cls, *params, page=1, page_size=10, is_dic=True, order_field=None, desc=True):
        """
        通用分页查询方法[8](@ref)
        :param is_dic: 是否返回字典
        :param page: 当前页码
        :param page_size: 每页数量
        :param order_field: 排序字段（默认使用update_time）
        :param desc: 是否降序
        :return: (结果列表, 总数量)
        """
        # 处理排序字段
        order_field = order_field or cls.update_time
        order_by = order_field.desc() if desc else order_field.asc()

        # 构建基础查询
        query = cls.select().where(*params).order_by(order_by)

        # 分页处理[1,7](@ref)
        total = query.count()
        paginated = query.paginate(page, page_size)

        if is_dic:
            return [obj.cus_model_to_dict() for obj in paginated], total

        return [obj for obj in paginated], total

    @classmethod
    def cus_query_to_dict(cls, *params, order_field=None, desc=True):
        # 构建基础查询
        query = cls.cus_query(*params, order_field=order_field, desc=desc)

        return [obj.cus_model_to_dict() for obj in query]

    @classmethod
    def cus_query(cls, *params, order_field=None, desc=True):
        """
        通用分页查询方法[8](@ref)
        :param param:
        :param order_field: 排序字段（默认使用update_time）
        :param desc: 是否降序
        :return: (结果列表, 总数量)
        """
        # 处理排序字段
        order_field = order_field or cls.update_time
        order_by = order_field.desc() if desc else order_field.asc()

        # 构建基础查询
        query = cls.select().where(*params).order_by(order_by)

        return query

    def cus_model_to_dict(self, date_format='%Y-%m-%d %H:%M:%S'):
        result = {}
        for field in self._meta.sorted_fields:
            key = field.name.lstrip('_')  # 自动去除前导下划线
            value = getattr(self, key, None)  # 优先获取属性值
            if value is None:
                value = getattr(self, field.name)

            if isinstance(value, datetime):
                value = value.strftime(date_format)
            elif isinstance(value, Enum):
                value = value.name  # 输出枚举名称

            result[key] = value
        return result
