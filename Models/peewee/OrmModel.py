from enum import Enum

from peewee import AutoField, CharField, IntegerField, SmallIntegerField, UUIDField, TextField
from Utils.logs.LoggingConfig import logger

from Models.peewee.BaseModel import BaseModel, mysql_db, MediumTextField


class TaskType(Enum):
    LAY = 1


class TaskState(Enum):
    # 推荐使用10为间隔的数值（如10,20,30...），便于后续插入中间状态：
    WAITING = 10
    DOWNLOADING = 20
    DOWNLOADED = 30
    PROCESSING = 40
    COMPLETED = 50
    FAILED = 60
    CANCELED = 70


# 任务组
class KBAnalysisGroup(BaseModel):
    id = AutoField(primary_key=True)  # 主键
    user_id = CharField(max_length=40)  # 用户名
    title = CharField(max_length=60, null=True)  # 标题
    description = CharField(max_length=255, null=True)  # 描述
    _state = SmallIntegerField(default=TaskState.WAITING.value,column_name="state")  # 状态

    @classmethod
    def state_equals(cls, state_enum: TaskState):
        """生成状态等于的条件表达式"""
        return cls._state == state_enum.value

    @property
    def state(self) -> TaskState:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskState(self._state)
        except ValueError:
            return TaskState.WAITING  # 默认值处理异常情况

    @state.setter
    def state(self, value: TaskState):
        """将枚举对象存入数据库字段"""
        if not isinstance(value, TaskState):
            raise ValueError("必须传入TaskType枚举类型")
        self._state = value.value


# 任务
class KBAnalysisTask(BaseModel):
    id = AutoField(primary_key=True)  # 主键
    group_id = IntegerField()  # 工作组ID
    file_name = CharField(null=True)  # 原始文件名
    file_url = CharField(null=True)  # 文件地址
    file_size = IntegerField(null=True,default=0)  # 文件大小
    chunk_count = IntegerField(null=True,default=0)
    deal_chunk_count = IntegerField(null=True,default=0)
    result = MediumTextField(null=True)
    _task_type = SmallIntegerField(column_name="task_type",default=TaskType.LAY.value)  # 类型
    _state = SmallIntegerField(column_name="state",default=TaskState.WAITING.value)  # 状态

    @classmethod
    def state_equals(cls, state_enum: TaskState):
        """生成状态等于的条件表达式"""
        return cls._state == state_enum.value

    @property
    def state(self) -> TaskState:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskState(self._state)
        except ValueError:
            return TaskState.WAITING  # 默认值处理异常情况

    @state.setter
    def state(self, value: TaskState):
        self._state = value.value

    @property
    def task_type(self) -> TaskType:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskType(self._task_type)
        except ValueError:
            return TaskType.LAY  # 默认值处理异常情况

    @task_type.setter
    def task_type(self, value: TaskType):
        self._task_type = value.value


def create_tables():  # 在文件底部添加连接测试
    try:
        mysql_db.connect(reuse_if_open=True)
        mysql_db.create_tables([KBAnalysisGroup, KBAnalysisTask])
        logger.info("表创建成功")  # 需要导入logger
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise
    finally:
        if not mysql_db.is_closed():
            mysql_db.close()