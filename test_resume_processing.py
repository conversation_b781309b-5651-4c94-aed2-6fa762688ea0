#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简历处理功能测试脚本
用于测试优化后的简历分块和信息提取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Agents.TalentAgentProcessing import TalentAgentProcessing
from Models.agent.TalentInfoProcessing import ContentTrunk
from Utils.logs.LoggingConfig import logger


def test_resume_segmentation():
    """测试简历分块功能"""
    
    # 测试用的简历样本
    sample_resume = """
    张三
    男，28岁
    手机：13812345678
    邮箱：<EMAIL>
    现居地址：北京市朝阳区
    求职意向：Python开发工程师
    
    个人简介：
    5年Python开发经验，熟悉Django、Flask框架，具有丰富的Web开发经验。
    
    工作经历：
    2020.03-2023.12  ABC科技有限公司  高级Python开发工程师
    负责公司核心业务系统的开发和维护，参与架构设计和技术选型。
    
    2018.07-2020.02  XYZ互联网公司  Python开发工程师
    负责电商平台后端开发，优化系统性能，提升用户体验。
    
    项目经历：
    电商平台重构项目（2021.06-2022.03）
    项目描述：对老旧电商平台进行全面重构，采用微服务架构
    技术栈：Python、Django、Redis、MySQL、Docker
    职责：负责用户模块和订单模块的开发
    
    智能推荐系统（2019.09-2020.01）
    项目描述：基于机器学习的商品推荐系统
    技术栈：Python、TensorFlow、Pandas、NumPy
    职责：负责推荐算法的实现和优化
    
    教育经历：
    2014.09-2018.06  清华大学  计算机科学与技术  本科
    主修课程：数据结构、算法设计、数据库原理、软件工程
    
    技能证书：
    - 软件设计师（中级）
    - AWS云计算认证
    - Python高级程序员认证
    """
    
    processor = TalentAgentProcessing()
    
    print("=" * 60)
    print("测试智能简历分块功能")
    print("=" * 60)
    
    try:
        # 测试智能分块
        result = processor.intelligent_resume_segmentation(sample_resume)
        
        print("\n分块结果：")
        print("-" * 40)
        
        print(f"基本信息长度: {len(result.basic_info or '')}")
        if result.basic_info:
            print(f"基本信息预览: {result.basic_info[:100]}...")
        
        print(f"\n工作经历长度: {len(result.work_experience or '')}")
        if result.work_experience:
            print(f"工作经历预览: {result.work_experience[:100]}...")
        
        print(f"\n教育经历长度: {len(result.education_experience or '')}")
        if result.education_experience:
            print(f"教育经历预览: {result.education_experience[:100]}...")
        
        print(f"\n项目经历长度: {len(result.project_experience or '')}")
        if result.project_experience:
            print(f"项目经历预览: {result.project_experience[:100]}...")
        
        # 显示内容摘要
        summary = result.get_content_summary()
        print(f"\n内容摘要: {summary}")
        
        # 验证分块有效性
        is_valid = result.is_valid()
        print(f"\n分块结果有效性: {'✓ 有效' if is_valid else '✗ 无效'}")
        
        return result
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        logger.error(f"简历分块测试失败: {str(e)}")
        return None


def test_different_resume_formats():
    """测试不同格式的简历"""
    
    formats = {
        "无明确标题格式": """
        李四，女，25岁，北京大学计算机系毕业
        联系方式：15912345678，<EMAIL>
        曾在腾讯工作2年，担任前端开发工程师
        参与过多个大型项目的开发
        熟悉React、Vue等前端框架
        """,
        
        "分散信息格式": """
        王五
        教育背景：北京理工大学软件工程专业
        联系电话：13712345678
        
        曾在阿里巴巴担任Java开发工程师
        工作时间：2019年至今
        
        技能：Java、Spring、MySQL
        项目：电商系统开发
        
        年龄：30岁，男
        现居：上海市浦东新区
        """,
        
        "英文混合格式": """
        Name: John Zhang
        Age: 32, Male
        Phone: +86 138-0000-0000
        Email: <EMAIL>
        
        Work Experience:
        Senior Software Engineer at Microsoft (2020-2023)
        Software Engineer at Google (2018-2020)
        
        Education:
        Master of Computer Science, Stanford University (2016-2018)
        Bachelor of Software Engineering, Tsinghua University (2012-2016)
        
        Projects:
        Cloud Computing Platform Development
        AI-powered Recommendation System
        """
    }
    
    processor = TalentAgentProcessing()
    
    print("\n" + "=" * 60)
    print("测试不同格式简历的处理能力")
    print("=" * 60)
    
    for format_name, resume_text in formats.items():
        print(f"\n测试格式: {format_name}")
        print("-" * 40)
        
        try:
            result = processor.intelligent_resume_segmentation(resume_text)
            summary = result.get_content_summary()
            
            print(f"分块成功，共识别 {summary['total_sections']} 个有效部分")
            print(f"基本信息: {summary['basic_info_length']} 字符")
            print(f"工作经历: {summary['work_experience_length']} 字符")
            print(f"教育经历: {summary['education_experience_length']} 字符")
            print(f"项目经历: {summary['project_experience_length']} 字符")
            
        except Exception as e:
            print(f"处理失败: {str(e)}")


def main():
    """主测试函数"""
    print("开始测试优化后的简历处理功能...")
    
    # 测试基本分块功能
    result = test_resume_segmentation()
    
    # 测试不同格式处理
    test_different_resume_formats()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    if result:
        print("\n✓ 基本功能测试通过")
        print("✓ 智能分块功能正常工作")
        print("✓ 支持多种简历格式")
    else:
        print("\n✗ 测试失败，请检查配置")


if __name__ == "__main__":
    main()
